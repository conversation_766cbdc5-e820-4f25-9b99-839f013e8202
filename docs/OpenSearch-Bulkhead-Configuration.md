# OpenSearch Bulkhead Configuration

## Overview

The OpenSearch service uses <PERSON>'s Bulkhead pattern to limit concurrent operations and prevent resource exhaustion. This document explains the configuration and troubleshooting of bulkhead-related issues.

## What is a Bulkhead?

A bulkhead is a resilience pattern that isolates resources to prevent cascading failures. In our case, it limits:
- **Maximum concurrent operations**: How many OpenSearch operations can run simultaneously
- **Maximum queue size**: How many operations can wait when all concurrent slots are occupied

## Configuration

### Default Values

```json
{
  "CommunicationSettings": {
    "CircuitBreakerThreshold": 3,
    "CircuitBreakerDuration": "00:00:10",
    "BulkheadMaxConcurrency": 25,
    "BulkheadMaxQueueSize": 500
  }
}
```

### Configuration Properties

| Property | Default | Description |
|----------|---------|-------------|
| `BulkheadMaxConcurrency` | 25 | Maximum number of concurrent OpenSearch operations |
| `BulkheadMaxQueueSize` | 500 | Maximum number of operations that can be queued |
| `CircuitBreakerThreshold` | 3 | Number of failures before circuit breaker opens |
| `CircuitBreakerDuration` | 10 seconds | How long circuit breaker stays open |

## Common Issues

### BulkheadRejectedException

**Error**: `Polly.Bulkhead.BulkheadRejectedException: The bulkhead semaphore and queue are full and execution was rejected.`

**Cause**: Too many concurrent operations are being attempted, exceeding both the concurrency limit and queue capacity.

**Solutions**:

1. **Increase Bulkhead Limits** (Quick fix):
   ```json
   {
     "CommunicationSettings": {
       "BulkheadMaxConcurrency": 50,
       "BulkheadMaxQueueSize": 1000
     }
   }
   ```

2. **Optimize Application Load**:
   - Implement batching for bulk operations
   - Add delays between operations
   - Use background processing for non-critical operations

3. **Monitor and Alert**:
   - Set up monitoring for bulkhead rejections
   - Alert when rejection rate exceeds threshold

## Monitoring

### Log Messages

The service now logs specific messages for bulkhead rejections:

```
[WARNING] OpenSearch bulkhead is full - too many concurrent operations. Content ID: {ContentId}. Consider increasing bulkhead limits or reducing load.
```

### Metrics to Monitor

- Bulkhead rejection rate
- Average operation duration
- Queue depth
- Concurrent operation count

## Tuning Guidelines

### When to Increase Limits

- High rejection rates (>5%)
- Consistent high load
- Fast OpenSearch cluster response times

### When to Decrease Limits

- Memory pressure
- Slow OpenSearch cluster
- Resource constraints

### Recommended Starting Points

| Environment | Concurrency | Queue Size |
|-------------|-------------|------------|
| Development | 10 | 100 |
| Testing | 25 | 500 |
| Production | 50-100 | 1000-2000 |

## Implementation Details

The bulkhead is configured in `DependencyInjection.cs` and wraps all OpenSearch operations in:
- `SearchAdapter.Save()`
- `SearchAdapter.Delete()`
- `SearchAdapter.InitializeIndex()`
- `SearchAdapter.DeleteIndex()`

Each method includes specific error handling for `BulkheadRejectedException` with appropriate logging.
