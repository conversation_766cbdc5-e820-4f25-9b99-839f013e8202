﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using AutoMapper.Internal;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Entities.V2;
using BlueGuava.ContentManagement.Common.Exceptions;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.Extensions.AWS.OpenSearch.Models.Indexing.V2;
using BlueGuava.Extensions.AWS.OpenSearch.Models.Mapping.V2;
using BlueGuava.Extensions.Configuration.Models;
using BlueGuava.Extensions.InMemory.Services;
using BlueGuava.Extensions.Logging;
using BlueGuava.Library;
using BlueGuava.Library.Common.BusinessEntities;
using BlueGuava.Library.Common.Enums;
using BlueGuava.Library.Interop;
using BlueGuava.MessageQueuing;
using BlueGuava.OrderedSearchResult;
using BlueGuava.Tracewind.Common.Models;
using CorrelationId;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using OpenSearch.Client;
using OpenSearch.Net;
using Polly;
using Polly.Bulkhead;
using Polly.Wrap;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace BlueGuava.ContentManagement.Service.OpenSearch.Implementation;

public class OpenSearchService : IOpenSearchService
{
    private static readonly char[] fieldSplit = new[] { ',' };
    private static readonly char[] sortSplit = new[] { ' ' };

    private static readonly IReadOnlyDictionary<string, string> propertyNames =
        typeof(ContentSearchItem).GetProperties()
            .Select(x => new { Key = x.Name, Value = x.Name })
            .ToDictionary(p => p.Key, p => p.Value, StringComparer.OrdinalIgnoreCase);

    private MustacheConfig mustacheConfig = new() { Scripts = new List<MustacheScriptDefinition>() };

    private static readonly PropertyCondition[] anyConditions = new[]
    {
        PropertyCondition.Any
    };

    private static readonly PropertyCondition[] rangeConditions = new[]
    {
        PropertyCondition.Greater,
        PropertyCondition.GreaterOrEqual,
        PropertyCondition.Less,
        PropertyCondition.LessOrEqual
    };

    private static readonly PropertyCondition[] equalityConditions = new[]
    {
        PropertyCondition.Equal,
        PropertyCondition.NotEqual,
        PropertyCondition.Exists,
        PropertyCondition.NotExists,
        PropertyCondition.Any
    };

    private readonly System.Text.Json.JsonSerializerOptions jsonOptions = new()
    {
        PropertyNameCaseInsensitive = true,
        ReadCommentHandling = System.Text.Json.JsonCommentHandling.Skip,
        AllowTrailingCommas = true
    };

    private readonly ILogger<OpenSearchService> logger;
    private readonly IFeatureManager featureManager;
    private readonly IOpenSearchClient client;
    private readonly IMapper mapper;
    private readonly ICorrelationContextAccessor correlationContextAccessor;
    private readonly IMessageQueue<TraceLogMessage> traceLog;
    private readonly ICacheService memoryCache;
    private readonly ModuleInfo moduleInfo;
    private readonly IndexDefinition indexdefinition = new ContentIndexDefinition();
    private readonly AsyncPolicyWrap wrappedPolicy;
    private readonly string mustacheQueryScripts;

    public OpenSearchService(
        ILogger<OpenSearchService> logger,
        IFeatureManager featureManager,
        IOpenSearchClient client,
        IMapper mapper,
        ICorrelationContextAccessor correlationContextAccessor,
        IMessageQueue<TraceLogMessage> traceLog,
        IOptions<ModuleInfo> moduleInfo,
        ICacheService memoryCache,
        IOptionsMonitor<CommunicationSettings> options,
        AsyncBulkheadPolicy bulkheadPolicy,
        IConfiguration configuration)
    {
        this.logger = logger;
        this.featureManager = featureManager;
        this.client = client;
        this.mapper = mapper;
        this.correlationContextAccessor = correlationContextAccessor;
        this.traceLog = traceLog;
        this.memoryCache = memoryCache;
        this.moduleInfo = moduleInfo.Value;
        mustacheQueryScripts = configuration["OpenSearch:Query:Script"] ?? "";

        wrappedPolicy = bulkheadPolicy.WrapAsync(Policy.Handle<NetworkConnectionException>().Or<Exception>()
            .WaitAndRetryAsync(options.CurrentValue.CircuitBreakerThreshold,
                i => options.CurrentValue.CircuitBreakerDuration,
                (ex, span) => logger.LogError("Circuit breaker open for {Span} because {Message}", span, ex.Message)
            ));
    }

    /*
        public async Task<SearchResult<Content>> PublicSearch(PublicContentSearch searchArgs, int pageSize, int pageIndex,
            CancellationToken cancellationToken = default)
        {
            var nothing = Enumerable.Empty<Content>();

            var searchRes = await memoryCache.GetOrCreateAsync(
                $"{searchArgs}_{pageIndex}_{pageSize}",
                async () =>
                {
                    return await wrappedPolicy.ExecuteAsync(async (cancel) =>
                    {
                        return await SearchResponse(new SearchDescriptor<ContentSearchItem>().From(pageSize * pageIndex)
                            .Size(pageSize)
                            .Index(Indices.Index(indexdefinition.Name))
                            .Query(qx => CreateKeywordQuery(qx, searchArgs?.Keyword)
                                         && CreateCommonFilter(searchArgs))
                            .Sort(SetupPublicSort), cancel);
                    }, cancellationToken);
                }
            );

            if (searchRes == null)
            {
                logger.Standards(correlationContextAccessor, nameof(OpenSearchService), nameof(Search))
                    .Log(LogLevel.Error, "Open search result is null");
                return new SearchResult<Content>(nothing, 0, 0, 0);
            }

            if (!searchRes.IsValid)
            {
                logger.Standards(correlationContextAccessor, nameof(OpenSearchService), nameof(Search))
                    .Log(LogLevel.Error, "Open search result is not valid: {DebugInformation}",
                        searchRes.DebugInformation);

                return new SearchResult<Content>(nothing, 0, 0, 0);
            }

            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(OpenSearchService), nameof(Search))
                    .Log(LogLevel.Debug, "DebugInformation: {DebugInformation}", searchRes.DebugInformation);

            var result = searchRes.Hits
                ?.Where(x => Guid.TryParse(x.Source?.ContentId, out _))
                ?.Select(x => mapper.Map<Content>(x.Source)) ?? nothing;

            return new SearchResult<Content>(result, (int)searchRes.Total, pageSize, pageIndex);
        }
    */

    #region ===================== Mustache Query

    public async Task<SyncResult> SyncMustacheScriptsFromFile()
    {
        if (string.IsNullOrEmpty(mustacheQueryScripts)) throw new ArgumentNullException("mustacheQueryScripts is empty");

        var json = mustacheQueryScripts;

        var parsed = System.Text.Json.JsonSerializer.Deserialize<MustacheConfig>(json, jsonOptions);
        if (parsed == null || parsed.Scripts == null || parsed.Scripts.Count == 0)
            throw new InvalidOperationException("No scripts defined in mustache config");

        ValidateDefinitions(parsed);

        var upserted = new List<string>();
        foreach (var def in parsed.Scripts)
        {
            var ok = await PutScript(def, default);
            if (!ok) throw new InvalidOperationException($"Failed to upsert script: {def.Id}");
            upserted.Add(def.Id);
        }

        mustacheConfig = parsed;
        return new SyncResult { ScriptCount = upserted.Count, Scripts = upserted };
    }

    public async Task<SearchResult<Content>?> ExecuteMustache(string scriptId, IDictionary<string, object> parameters, int pageSize, int pageIndex, CancellationToken cancel = default)
    {
        if (string.IsNullOrWhiteSpace(scriptId)) throw new ArgumentException("scriptId is required");
        parameters ??= new Dictionary<string, object>();

        var def = FindDefinition(scriptId);
        ValidateParams(def, parameters);

        var nothing = Enumerable.Empty<Content>();
        var from = pageSize * pageIndex;
        var searchRes = await client.SearchTemplateAsync<ContentSearchItem>(s => s
            .Index(Indices.Index(indexdefinition.Name))
            .Id(scriptId)
            .Params(p =>
            {
                foreach (var param in parameters)
                {
                    p.Add(param.Key, param.Value);
                }
                p.Add("from", from);
                p.Add("size", pageSize);
                return p;
            }), cancel);

        if (!searchRes.IsValid)
        {
            var reason = searchRes.ServerError?.Error?.Reason ?? searchRes.OriginalException?.Message ?? "Unknown error";
            throw new InvalidOperationException($"OpenSearch template execution failed: {reason}");
        }

        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(OpenSearchService), nameof(Search))
                .Log(LogLevel.Debug, "DebugInformation: {DebugInformation}", searchRes.DebugInformation);

        ExtractSearchQuery(pageIndex, searchRes);

        var result = searchRes.Hits
            ?.Where(x => Guid.TryParse(x.Source?.ContentId, out _))
            ?.Select(x => mapper.Map<Content>(x.Source)) ?? nothing;

        return new SearchResult<Content>(result, (int)searchRes.Total, pageSize, pageIndex);
    }

    private MustacheScriptDefinition FindDefinition(string scriptId)
    {
        if (string.IsNullOrEmpty(mustacheQueryScripts)) throw new ArgumentNullException("mustacheQueryScripts is empty");

        var parsed = System.Text.Json.JsonSerializer.Deserialize<MustacheConfig>(mustacheQueryScripts, jsonOptions);
        var def = parsed.Scripts.Find(s => string.Equals(s.Id, scriptId, StringComparison.Ordinal));
        if (def == null) throw new KeyNotFoundException($"Script '{scriptId}' not found; sync from file first");
        return def;
    }

    private void ValidateDefinitions(MustacheConfig cfg)
    {
        var ids = new HashSet<string>(StringComparer.Ordinal);
        foreach (var def in cfg.Scripts)
        {
            if (string.IsNullOrWhiteSpace(def.Id)) throw new InvalidOperationException("A script has empty id");
            if (!ids.Add(def.Id)) throw new InvalidOperationException($"Duplicate script id '{def.Id}'");
            if (def.Source is null) throw new InvalidOperationException($"Script '{def.Id}' has null source");

            //var node = System.Text.Json.JsonSerializer.SerializeToNode(def.Source, jsonOptions) as System.Text.Json.Nodes.JsonObject;
            //if (node is null || node["query"] is null)
            //    throw new InvalidOperationException($"Script '{def.Id}' source must contain a 'query' object");
        }
    }

    private void ValidateParams(MustacheScriptDefinition def, IDictionary<string, object> parameters)
    {

        if (def.ParamRules == null || def.ParamRules.Count == 0) return;

        foreach (var rule in def.ParamRules)
        {
            var name = rule.Key;
            var spec = rule.Value;

            if (spec.Required && !parameters.ContainsKey(name))
                throw new ArgumentException($"Missing required parameter '{name}' for script '{def.Id}'");

            if (!parameters.ContainsKey(name)) continue;

            var val = parameters[name];

            if (!ValidateType(spec.Type, val))
                throw new ArgumentException($"Parameter '{name}' type mismatch. Expected {spec.Type}");

            if (spec.Type == "string" && val is string str)
            {
                if (!string.IsNullOrEmpty(spec.Regex) && !System.Text.RegularExpressions.Regex.IsMatch(str, spec.Regex))
                    throw new ArgumentException($"Parameter '{name}' does not match pattern '{spec.Regex}'");
                if (spec.MinLength.HasValue && str.Length < spec.MinLength.Value)
                    throw new ArgumentException($"Parameter '{name}' shorter than {spec.MinLength}");
                if (spec.MaxLength.HasValue && str.Length > spec.MaxLength.Value)
                    throw new ArgumentException($"Parameter '{name}' longer than {spec.MaxLength}");
            }

            if (spec.Type == "number" && TryToDouble(val, out var d))
            {
                if (spec.Min.HasValue && d < spec.Min.Value)
                    throw new ArgumentException($"Parameter '{name}' below minimum {spec.Min}");
                if (spec.Max.HasValue && d > spec.Max.Value)
                    throw new ArgumentException($"Parameter '{name}' above maximum {spec.Max}");
            }
        }
    }

    private bool ValidateType(string? type, object val)
    {
        var t = type?.ToLowerInvariant();
        return t switch
        {
            "string" => val is string,
            "number" => val is sbyte || val is byte || val is short || val is ushort || val is int || val is uint ||
                        val is long || val is ulong || val is float || val is double || val is decimal,
            "boolean" => val is bool,
            "array" => val is System.Collections.IEnumerable && val is not string,
            "object" => val is not null && !(val is string) && !(val is System.Collections.IEnumerable),
            null or "" => true,
            _ => false
        };
    }

    private bool TryToDouble(object val, out double d)
    {
        switch (val)
        {
            case sbyte v: d = v; return true;
            case byte v: d = v; return true;
            case short v: d = v; return true;
            case ushort v: d = v; return true;
            case int v: d = v; return true;
            case uint v: d = v; return true;
            case long v: d = v; return true;
            case ulong v: d = v; return true;
            case float v: d = v; return true;
            case double v: d = v; return true;
            case decimal v: d = (double)v; return true;
            default: d = 0; return false;
        }
    }

    private async Task<bool> PutScript(MustacheScriptDefinition def, CancellationToken cancel)
    {
        var body = new { script = new { lang = "mustache", source = def.Source } };
        var path = $"/_scripts/{Uri.EscapeDataString(def.Id)}";

        var resp = await client.LowLevel.DoRequestAsync<global::OpenSearch.Net.StringResponse>(
            global::OpenSearch.Net.HttpMethod.POST,
            path,
            cancel,
            global::OpenSearch.Net.PostData.Serializable(body)).ConfigureAwait(false);

        var ok = resp.HttpStatusCode is >= 200 and < 300;
        if (!ok) logger.LogError("Failed to update  script {Id}. Status {Status} Body {Body}", def.Id, resp.HttpStatusCode, resp.Body);
        return ok;
    }

    private static object NormalizeMustacheParam(object value)
    {
        if (value is null) return null!;

        if (value is JValue jv) return jv.Value;

        if (value is JArray ja)
            return ja.Select(NormalizeMustacheParam).ToArray();

        if (value is JObject jo)
            return jo.Properties().ToDictionary(
                p => p.Name,
                p => NormalizeMustacheParam(p.Value)
            );

        if (value is System.Collections.IEnumerable e && value is not string)
            return e.Cast<object>().Select(NormalizeMustacheParam).ToArray();

        return value; // already a CLR primitive or supported type
    }

    #endregion ===================== Mustache Query


    public async Task<SearchResult<Content>?> Search(ContentSearch? searchArgs,
        int pageSize,
        int pageIndex,
        string? sortBy,
        CancellationToken cancellationToken = default)
    {
        var nothing = Enumerable.Empty<Content>();
        if (searchArgs == null) return new SearchResult<Content>(nothing, 0, 0, 0);

        //logger.Standards(correlationContextAccessor, nameof(OpenSearchService), nameof(Search))
        //        .Log(LogLevel.Debug, ContentSearch.LogFormat, searchArgs.GetValues());


        var searchRes = await SearchResponse(new SearchDescriptor<ContentSearchItem>()
                            .From(pageSize * pageIndex).Size(pageSize)
                            .Index(Indices.Index(indexdefinition.Name))
                            .TrackTotalHits(true)
                            .Query(qx =>
                                CreateKeywordQuery(qx, searchArgs?.Query)
                                && CreateCommonFilter(searchArgs)
                            )
                            .Sort(so => SetupSort(so, sortBy)), cancellationToken);
        /*
                var searchRes = await memoryCache.GetOrCreateAsync(
                    $"{searchArgs.ToString()}_{pageIndex}_{pageSize}_{sortBy}",
                    async () =>
                    {
                        return await wrappedPolicy.ExecuteAsync(async (cancel) =>
                        {
                            return await SearchResponse(new SearchDescriptor<ContentSearchItem>()
                                    .From(pageSize * pageIndex).Size(pageSize)
                                    .Index(Indices.Index(indexdefinition.Name))
                                    .Query(qx =>
                                        CreateKeywordQuery(qx, searchArgs?.Query)
                                        && CreateCommonFilter(searchArgs)
                                    )
                                    .Sort(so => SetupSort(so, sortBy))
                                , cancel
                            );
                        }, cancellationToken);
                    });
        */
        if (searchRes == null)
        {
            logger.Standards(correlationContextAccessor, nameof(OpenSearchService), nameof(Search))
                .Log(LogLevel.Error, "Open search result is null");
            return new SearchResult<Content>(nothing, 0, 0, 0);
        }

        if (!searchRes.IsValid)
        {
            logger.Standards(correlationContextAccessor, nameof(OpenSearchService), nameof(Search))
                .Log(LogLevel.Error, "Open search result is not valid: {DebugInformation}",
                    searchRes.DebugInformation);

            return new SearchResult<Content>(nothing, 0, 0, 0);
        }

        if (logger.IsEnabled(LogLevel.Debug))
            logger.Standards(correlationContextAccessor, nameof(OpenSearchService), nameof(Search))
                .Log(LogLevel.Debug, "DebugInformation: {DebugInformation}", searchRes.DebugInformation);

        ExtractSearchQuery(pageIndex, searchRes);

        var result = searchRes.Hits
            ?.Where(x => Guid.TryParse(x.Source?.ContentId, out _))
            ?.Select(x => mapper.Map<Content>(x.Source)) ?? nothing;

        return new SearchResult<Content>(result, (int)searchRes.Total, pageSize, pageIndex);
    }

    public async Task<(SearchResult<Content>? Hits, IReadOnlyDictionary<string, Dictionary<string, long>> GroupCounts)> Search(
        ContentSearch? searchArgs,
        DecisionRules? decisionRules,
        OrderRules? orderingRules,
        int pageSize,
        int pageIndex,
        CancellationToken cancellationToken = default,
        IList<string>? groupByFields = null
    )
    {
        //logger.LogInformation("Search Query - Args: {@SearchArgs} Decision Rules: {@DecisionRules} Ordering Rules : {@OrderingRules}",  searchArgs ,decisionRules, orderingRules);

        var nothing = Enumerable.Empty<Content>();
        try
        {
            var searchDescriptor = new SearchDescriptor<ContentSearchItem>()
                                .MinScore(1)
                                .From(pageSize * pageIndex).Size(pageSize)
                                .Index(Indices.Index(indexdefinition.Name))
                                .Query(qx => CreateKeywordQuery(qx, searchArgs?.Query)
                                    && CreateCommonFilter(searchArgs)
                                    && CreateCommonFilter(decisionRules, searchArgs?.Radius ?? 5))
                                //.Query(qx => qx.MatchAll() && CreateCommonFilter(decisionRules))
                                .Sort(so => SetupSort(so, orderingRules));

            //logger.LogInformation("Search Query - SearchDescriptor: {@searchDescriptor}",  searchDescriptor);

            if (groupByFields?.Any() == true)
            {
                searchDescriptor = searchDescriptor.Aggregations(a =>
                {
                    var agg = new AggregationContainerDescriptor<CustomerSearchItem>();
                    foreach (var field in groupByFields)
                    {
                        a.Terms($"group_by_{field}", t => t
                            .Field(field)
                            .Size(1000));
                    }
                    return a;
                });
            }

            var searchRes = await SearchResponse(searchDescriptor, cancellationToken);

            /*
                        var searchRes = await memoryCache.GetOrCreateAsync(
                            $"{searchArgs.ToString()}_" +
                            $"{(decisionRules != null ? string.Join("_", decisionRules.Select(x => x.ToJson())) : string.Empty)}_" +
                            $"{(orderingRules != null ? string.Join("_", orderingRules.Select(x => x.ToJson())) : string.Empty)}_" +
                            $"{pageIndex}_{pageSize}",
                            async () =>
                            {
                                return await wrappedPolicy.ExecuteAsync(async (cancel) =>
                                {
                                    return await SearchResponse(new SearchDescriptor<ContentSearchItem>()
                                            .MinScore(1)
                                            .From(pageSize * pageIndex).Size(pageSize)
                                            .Index(Indices.Index(indexdefinition.Name))
                                            .Query(qx => qx.MatchAll() && CreateCommonFilter(decisionRules))
                                            .Sort(so => SetupSort(so, orderingRules)),
                                        cancel
                                    );
                                }, cancellationToken);
                            });
            */
            if (searchRes == null)
            {
                logger.Standards(correlationContextAccessor, nameof(OpenSearchService), nameof(Search))
                    .Log(LogLevel.Error, "Open search result is null");
                return (new SearchResult<Content>(nothing, 0, 0, 0), new Dictionary<string, Dictionary<string, long>>());
            }

            if (!searchRes.IsValid)
            {
                //this error is expected when index is empty
                if ((searchRes.ServerError?.Error?.Reason?.Contains("No mapping found for") ?? false)
                    || (searchRes.ServerError?.Error?.RootCause?.FirstOrDefault()?.Reason
                        ?.Contains("No mapping found for") ?? false)
                   )
                    return (new SearchResult<Content>(nothing, 0, 0, 0), new Dictionary<string, Dictionary<string, long>>());

                if (searchRes.ServerError?.Error?.CausedBy?.Reason?.Contains(
                        "Text fields are not optimised for operations") ?? false)
                    // throw new ArgumentException(
                    //     $"Open search result is not valid: {searchRes.ServerError.Error.CausedBy.Reason}");
                    return (new SearchResult<Content>(nothing, 0, 0, 0), new Dictionary<string, Dictionary<string, long>>());

                if (searchRes.ServerError?.Error?.RootCause?.FirstOrDefault()?.Reason
                        ?.Contains("Failed to parse query") ?? false)
                    throw new ArgumentException(
                        $"Open search result is not valid: {searchRes.ServerError?.Error?.RootCause?.FirstOrDefault()?.Reason}");

                logger.Standards(correlationContextAccessor, nameof(OpenSearchService), nameof(Search))
                    .Log(LogLevel.Error, "Open search result is not valid: {DebugInformation}",
                        searchRes.DebugInformation);

                throw new ArgumentException($"Open search result is not valid: {searchRes.DebugInformation}");
            }

            //send trace log with OpenSearch query only in the first iteration
            ExtractSearchQuery(pageIndex, searchRes);

            var resultSub = searchRes.Hits
                ?.Where(x => Guid.TryParse(x.Source?.ContentId, out _))
                ?.Select(x => mapper.Map<Content>(x.Source)) ?? nothing;
            var searchResult = new SearchResult<Content>(resultSub, (int)searchRes.Total, pageSize, pageIndex);

            var groupCounts = new Dictionary<string, Dictionary<string, long>>();
            if (groupByFields?.Any() == true)
            {
                foreach (var field in groupByFields)
                {
                    var aggKey = $"group_by_{field}";
                    var terms = searchRes.Aggregations.Terms(aggKey);
                    if (terms != null)
                    {
                        var buckets = terms.Buckets
                            .ToDictionary(
                                b => b.Key.ToString(),
                                b => b.DocCount ?? 0
                            );
                        groupCounts[field] = buckets;
                    }
                }
            }
            return (searchResult, groupCounts);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to search {@SearchArgs}", new
            {
                decisionRules,
                orderingRules,
                pageSize,
                pageIndex
            });
            return (new SearchResult<Content>(nothing, 0, 0, 0), new Dictionary<string, Dictionary<string, long>>());
        }
    }

    public async Task<int> Count(
      ContentSearch? searchArgs,
      DecisionRules? decisionRules,
      OrderRules? orderingRules,
      CancellationToken cancellationToken = default)
    {
        try
        {
            var countDescriptor = new CountDescriptor<ContentSearchItem>()
                .Index(Indices.Index(indexdefinition.Name))
                .Query(qx => CreateKeywordQuery(qx, searchArgs?.Query)
                             && CreateCommonFilter(searchArgs)
                             && CreateCommonFilter(decisionRules, searchArgs?.Radius ?? 5));

            var countRes = await client.CountAsync<ContentSearchItem>(c => countDescriptor, cancellationToken);

            if (countRes == null)
            {
                logger.Standards(correlationContextAccessor, nameof(OpenSearchService), nameof(Count))
                      .Log(LogLevel.Error, "OpenSearch count result is null");
                return 0;
            }

            if (!countRes.IsValid)
            {
                // same error handling style as in Search
                if ((countRes.ServerError?.Error?.Reason?.Contains("No mapping found for") ?? false)
                    || (countRes.ServerError?.Error?.RootCause?.FirstOrDefault()?.Reason
                        ?.Contains("No mapping found for") ?? false))
                    return 0;

                if (countRes.ServerError?.Error?.RootCause?.FirstOrDefault()?.Reason
                        ?.Contains("Failed to parse query") ?? false)
                    throw new ArgumentException(
                        $"OpenSearch count query is not valid: {countRes.ServerError?.Error?.RootCause?.FirstOrDefault()?.Reason}");

                logger.Standards(correlationContextAccessor, nameof(OpenSearchService), nameof(Count))
                      .Log(LogLevel.Error, "OpenSearch count query is not valid: {DebugInformation}",
                          countRes.DebugInformation);

                throw new ArgumentException($"OpenSearch count query is not valid: {countRes.DebugInformation}");
            }

            return (int)countRes.Count;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to count {@SearchArgs}", new
            {
                decisionRules,
                orderingRules
            });
            return 0;
        }
    }


    public async Task<GroupPageResult> SearchGroupsOnly(
        ContentSearch? searchArgs,
        DecisionRules? decisionRules,
        OrderRules? orderingRules,
        int pageSize,
        int pageIndex,
        CancellationToken cancellationToken = default,
        IList<string>? groupByFields = null)
    {
        var empty = new GroupPageResult
        {
            Items = Array.Empty<GroupBucket>(),
            PageIndex = pageIndex,
            PageSize = pageSize,
            EstimatedTotal = 0
        };

        try
        {
            if (groupByFields == null || groupByFields.Count == 0)
                return empty;

            var keywordFields = groupByFields.ToList();

            var baseDescriptor = new SearchDescriptor<ContentSearchItem>()
                .Size(0)
                .Index(Indices.Index(indexdefinition.Name))
                .Query(qx => CreateKeywordQuery(qx, searchArgs?.Query)
                             && CreateCommonFilter(searchArgs)
                             && CreateCommonFilter(decisionRules, searchArgs?.Radius ?? 5));

            bool singleField = keywordFields.Count == 1;
            string singleFieldName = singleField ? keywordFields[0] : string.Empty;

            var firstRequest = baseDescriptor.Aggregations(a =>
            {
                a = a.Composite("group_all", c =>
                    c.Sources(src =>
                    {
                        foreach (var f in keywordFields)
                            src.Terms($"group_by_{f}", t => t.Field(f));
                        return src;
                    })
                    .Size(pageSize)
                );

                if (singleField)
                    a = a.Cardinality("total_distinct", ca => ca.Field(singleFieldName));

                return a;
            });

            CompositeKey? after = null;
            if (pageIndex > 0)
            {
                for (int i = 0; i < pageIndex; i++)
                {
                    var navReq = baseDescriptor.Aggregations(a =>
                        a.Composite("group_all", c =>
                            c.Sources(src =>
                            {
                                foreach (var f in keywordFields)
                                    src.Terms($"group_by_{f}", t => t.Field(f));
                                return src;
                            })
                            .Size(pageSize)
                            .After(after)
                        )
                    );

                    var navRes = await SearchResponse(navReq, cancellationToken);
                    if (navRes == null || !navRes.IsValid)
                        return empty;

                    var navComp = navRes.Aggregations.Composite("group_all");
                    if (navComp == null || navComp.Buckets == null || navComp.Buckets.Count == 0)
                        return empty;

                    after = navComp.AfterKey;
                    if (after == null)
                        break;
                }
            }

            var pageReq = firstRequest;
            if (after != null)
            {
                pageReq = baseDescriptor.Aggregations(a =>
                {
                    a = a.Composite("group_all", c =>
                        c.Sources(src =>
                        {
                            foreach (var f in keywordFields)
                                src.Terms($"group_by_{f}", t => t.Field(f));
                            return src;
                        })
                        .Size(pageSize)
                        .After(after)
                    );

                    if (singleField)
                        a = a.Cardinality("total_distinct", ca => ca.Field(singleFieldName));

                    return a;
                });
            }

            var pageRes = await SearchResponse(pageReq, cancellationToken);
            if (pageRes == null || !pageRes.IsValid)
                return empty;

            var comp = pageRes.Aggregations.Composite("group_all");
            if (comp == null || comp.Buckets == null || comp.Buckets.Count == 0)
                return new GroupPageResult
                {
                    Items = Array.Empty<GroupBucket>(),
                    PageIndex = pageIndex,
                    PageSize = pageSize,
                    EstimatedTotal = singleField
                        ? (long?)(pageRes.Aggregations.Cardinality("total_distinct")?.Value ?? 0)
                        : null
                };

            long? estimatedTotal = null;
            if (singleField)
            {
                var card = pageRes.Aggregations.Cardinality("total_distinct");
                if (card != null && card.Value.HasValue)
                    estimatedTotal = (long)card.Value.Value;
            }

            static bool TryGetKeyAsString(CompositeKey key, string sourceName, out string value)
            {
                if (key.TryGetValue(sourceName, out string s))
                {
                    value = s;
                    return true;
                }
                if (key.TryGetValue(sourceName, out double d))
                {
                    value = d.ToString(CultureInfo.InvariantCulture);
                    return true;
                }
                value = string.Empty;
                return false;
            }

            var items = new List<GroupBucket>(comp.Buckets.Count);

            if (singleField)
            {
                var sourceName = $"group_by_{singleFieldName}";
                foreach (var b in comp.Buckets)
                {
                    if (!TryGetKeyAsString(b.Key, sourceName, out var keyStr) || string.IsNullOrEmpty(keyStr))
                        continue;

                    items.Add(new GroupBucket
                    {
                        Field = singleFieldName,
                        Key = keyStr,
                        Count = b.DocCount ?? 0
                    });
                }
            }
            else
            {
                foreach (var b in comp.Buckets)
                {
                    var dict = new Dictionary<string, string>(StringComparer.Ordinal);
                    foreach (var f in keywordFields)
                    {
                        var sourceName = $"group_by_{f}";
                        if (TryGetKeyAsString(b.Key, sourceName, out var keyStr) && !string.IsNullOrEmpty(keyStr))
                            dict[f] = keyStr;
                    }

                    if (dict.Count == 0) continue;

                    items.Add(new GroupBucket
                    {
                        Keys = dict,
                        Count = b.DocCount ?? 0
                    });
                }
            }

            return new GroupPageResult
            {
                Items = items,
                PageIndex = pageIndex,
                PageSize = pageSize,
                EstimatedTotal = estimatedTotal
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "SearchGroupsOnly failed. Args: {@Args}", new
            {
                pageSize,
                pageIndex,
                groupByFields
            });
            return new GroupPageResult
            {
                Items = Array.Empty<GroupBucket>(),
                PageIndex = pageIndex,
                PageSize = pageSize,
                EstimatedTotal = null
            };
        }
    }



    private async Task<ISearchResponse<T>?> SearchResponse<T>(SearchDescriptor<T> descriptor, CancellationToken cancel)
            where T : SearchItemDocumentBase
    {
        var response = await client.SearchAsync<T>(descriptor, cancel);

        if (!(response?.IsValid ?? false) && response?.ServerError?.Status > 400)
            throw new NetworkConnectionException(
                $"Open search result is not valid: {response?.ServerError?.Error?.Reason}");

        return response;
    }

    private void ExtractSearchQuery(int pageIndex, ISearchResponse<ContentSearchItem> searchRes)
    {
        if (pageIndex > 0) return;
        try
        {

            var debugInfo = searchRes.DebugInformation ?? string.Empty;

            if (string.IsNullOrEmpty(debugInfo)) return;

            var pFrom = debugInfo.IndexOf("# Request:", StringComparison.InvariantCultureIgnoreCase) +
                        "# Request:".Length;
            var pTo = debugInfo.LastIndexOf("# Response:", StringComparison.InvariantCultureIgnoreCase);
            var openSearchQuery = debugInfo.Substring(pFrom, pTo - pFrom);

            logger.LogInformation("OpenSearch Result Count: {Result} Query: {Query}", searchRes.HitsMetadata?.Total?.Value, openSearchQuery ?? "No Query");

        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to extract OpenSearch query {@SearchResult}", searchRes);
        }
    }

    private QueryContainer CreateQuery(QueryContainerDescriptor<ContentSearchItem> queryDescriptor, string? query)
    {
        if (string.IsNullOrEmpty(query))
            return queryDescriptor.MatchAll();

        return queryDescriptor.QueryString(m => m
            .Query(query.RemoveDiacritics()?.ToLower())
            .Type(TextQueryType.BestFields)
        );
    }

    private QueryContainer CreateKeywordQuery(QueryContainerDescriptor<ContentSearchItem> queryDescriptor,
        string? query)
    {
        if (string.IsNullOrEmpty(query))
            return queryDescriptor.MatchAll();

        //TO-DO
        return queryDescriptor.QueryString(m => m
            .Query($"*{query}*").AllowLeadingWildcard(true)
            .Fields(ff => ff
                .Field(f => f.Id)
                .Field(f => f.Title)
            //.Field(f => f.OriginalTitle)
            //.Field(f => f.Keywords)
            )
            .Type(TextQueryType.BestFields)
        );
    }

    private static BoolQuery CreateKeywordFilter(ContentSearch? searchArgs)
    {
        var queryFilter = new List<QueryContainer>();
        if (searchArgs == null)
            return
                new BoolQuery
                {
                    Filter = queryFilter
                };

        queryFilter.Add(new BoolQuery
        {
            MinimumShouldMatch = 1,
            Should = new QueryContainer[]
                {
                    new WildcardQuery
                    {
                        CaseInsensitive = true,
                        Value = $"*{searchArgs.Query}*",
                        Field = nameof(ContentSearchItem.OriginalTitle)
                    },
                    new WildcardQuery
                    {
                        CaseInsensitive = true,
                        Value = $"*{searchArgs.Query}*",
                        Field = nameof(ContentSearchItem.Title)
                    },
                    new MatchPhraseQuery()
                    {
                        Field = nameof(ContentSearchItem.Id),
                        Query = $"{searchArgs.Query}"
                    }
                }
        }
        );

        return new BoolQuery
        {
            Filter = queryFilter
        };
    }
    /*
        private static BoolQuery CreateCommonFilter(PublicContentSearch searchArgs)
        {
            var queryFilter = new List<QueryContainer>();

            queryFilter.Add(GetMandatoryFieldQuery(new TermQuery
            {
                Field = $"{nameof(ContentSearchItem.Published)}",
                Value = true
            }));

            if (searchArgs.Types != null && searchArgs.Types.Any())
                queryFilter.Add(GetOptionalContentTypeQuery(searchArgs));

            if (searchArgs.Cities != null && searchArgs.Cities.Length > 0)
                queryFilter.Add(GetOptionalCitiesQuery(searchArgs));

            if (searchArgs.Countries != null && searchArgs.Countries.Length > 0)
                queryFilter.Add(GetOptionalCountriesQuery(searchArgs));

            return new BoolQuery
            {
                Filter = queryFilter
            };
        }*/

    private static BoolQuery GetOptionalCountriesQuery(IContentSearch searchArgs)
    {
        return new BoolQuery
        {
            Must = new QueryContainer[]
            {
                new TermsQuery
                {
                    Field = nameof(ContentSearchItem.Countries),
                    Terms = searchArgs.Countries.Select(s => s.ToLower())
                }
            }
        };
    }

    private static BoolQuery GetOptionalCitiesQuery(IContentSearch searchArgs)
    {
        return new BoolQuery
        {
            Must = new QueryContainer[]
            {
                new TermsQuery
                {
                    Field = nameof(ContentSearchItem.Cities),
                    Terms = searchArgs.Cities.Select(s => s.ToLower())
                }
            }
        };
    }

    private BoolQuery CreateCommonFilter(ContentSearch? searchArgs)
    {
        var queryFilter = new List<QueryContainer>();

        if (searchArgs == null)
            return new BoolQuery
            {
                Filter = queryFilter
            };

        if (searchArgs.Types != null)
            queryFilter.Add(GetOptionalContentTypeQuery(searchArgs));

        if (!string.IsNullOrEmpty(searchArgs.ExternalId))
            queryFilter.Add(GetMandatoryFieldQuery(new TermQuery
            {
                Field = nameof(ContentSearchItem.ExternalId),
                Value = searchArgs.ExternalId
            }));

        if (!string.IsNullOrEmpty(searchArgs.ReferenceId))
            queryFilter.Add(GetMandatoryFieldQuery(new TermQuery
            {
                Field = nameof(ContentSearchItem.ReferenceId),
                Value = searchArgs.ReferenceId
            }));

        if (searchArgs.Published != null)
            queryFilter.Add(GetMandatoryFieldQuery(new TermQuery
            {
                Field = $"{nameof(ContentSearchItem.Published)}",
                Value = searchArgs.Published
            }));

        if (searchArgs.MarkerFilters?.Count > 0)
            searchArgs.MarkerFilters.ForEach(filter => { AddMarkerEqualityFilter(filter, queryFilter); });

        if (searchArgs.OrganizationFilters?.Count() > 0)
        {
            var orgFilter = GetOptionalOrganisationQuery(searchArgs);
            if (orgFilter != null)
                queryFilter.Add(orgFilter);
        }

        if (searchArgs.MinDuration.HasValue || searchArgs.MaxDuration.HasValue)
            queryFilter.Add(GetMandatoryFieldQuery(new TermRangeQuery
            {
                Field = $"{nameof(ContentSearchItem.Duration)}",
                GreaterThanOrEqualTo = searchArgs.MinDuration?.ToString("D10"),
                LessThanOrEqualTo = searchArgs.MaxDuration?.ToString("D10")
            }));

        SetDateRangeQuery(searchArgs, queryFilter);

        if (!string.IsNullOrEmpty(searchArgs.OwnerId) && searchArgs.OwnerId != Guid.Empty.ToString())
            queryFilter.Add(GetMandatoryFieldQuery(new TermQuery
            {
                Field = nameof(ContentSearchItem.OwnerId),
                Value = searchArgs.OwnerId
            }));

        if (searchArgs.Cities is { Length: > 0 }) queryFilter.Add(GetOptionalCitiesQuery(searchArgs));

        if (searchArgs.Countries is { Length: > 0 }) queryFilter.Add(GetOptionalCountriesQuery(searchArgs));

        if (!string.IsNullOrEmpty(searchArgs.Genre)) AddTagContainsFilter("Genre", searchArgs.Genre, queryFilter);

        if (!string.IsNullOrEmpty(searchArgs.Category))
            AddTagContainsFilter("Category", searchArgs.Category, queryFilter);

        if (searchArgs.Latitude.HasValue && searchArgs.Longitude.HasValue)
        {
            queryFilter.Add(new GeoDistanceQuery
            {
                Field = Infer.Field<ContentSearchItem>(f => f.Location),
                Distance = $"{searchArgs.Radius}km",
                Location = new GeoLocation(searchArgs.Latitude.Value, searchArgs.Longitude.Value)
            });
        }

        return new BoolQuery
        {
            Filter = queryFilter
        };
    }

    private static void SetDateRangeQuery(ContentSearch searchArgs, ICollection<QueryContainer> queryFilter)
    {
        if (searchArgs.CreatedFrom.HasValue || searchArgs.CreatedUntil.HasValue)
            queryFilter.Add(GetMandatoryFieldQuery(new DateRangeQuery
            {
                Field = $"{nameof(ContentSearchItem.CreatedDate)}",
                GreaterThanOrEqualTo = searchArgs.CreatedFrom.HasValue
                    ? (DateMath)searchArgs.CreatedFrom.Value
                    : null,
                LessThanOrEqualTo = searchArgs.CreatedUntil.HasValue
                    ? (DateMath)searchArgs.CreatedUntil.Value
                    : null
            }));

        if (searchArgs.ModifiedFrom.HasValue || searchArgs.ModifiedUntil.HasValue)
            queryFilter.Add(GetMandatoryFieldQuery(new DateRangeQuery
            {
                Field = $"{nameof(ContentSearchItem.LastModifiedDate)}",
                GreaterThanOrEqualTo = searchArgs.ModifiedFrom.HasValue
                    ? (DateMath)searchArgs.ModifiedFrom.Value
                    : null,
                LessThanOrEqualTo = searchArgs.ModifiedUntil.HasValue
                    ? (DateMath)searchArgs.ModifiedUntil.Value
                    : null
            }));
    }

    private static BoolQuery CreateCommonFilter(DecisionRules? decisionRules, int radius)
    {
        var queryFilter = new List<QueryContainer>();

        if (decisionRules == null)
            return new BoolQuery
            {
                Filter = queryFilter
            };

        var manualContents = decisionRules.Where(x =>
            x.PropertyName.Equals("Content:Id") && !string.IsNullOrEmpty(x.PropertyValue));

        if (manualContents.Any())
            queryFilter.Add(GetOptionalIdQuery(manualContents.ToList()));
        else
            decisionRules.ForEach(rule =>
            {
                AddRangeCondition(rule, queryFilter);
                AddEqualityFilter(rule, queryFilter);
                AddContainsFilter(rule, queryFilter, radius);
                AddAnyFilter(rule, queryFilter);
            });

        /*
        queryFilter.Add(GetMandatoryFieldQuery(new TermQuery
        {
            Field = $"{nameof(ContentSearchItem.Published)}",
            Value = true
        })); 
        */

        return new BoolQuery
        {
            Filter = queryFilter
        };
    }

    private static void AddEqualityFilter(DecisionRule rule, List<QueryContainer> queryFilter)
    {
        if (!equalityConditions.Contains(rule.Condition)) return;

        if (rule.PropertyName.StartsWith("Content:Labels:"))
        {
            AddTagEqualityFilter(rule, queryFilter);
        }
        else
        {
            var propertyName = PropertyNameConverter(rule.PropertyName);
            var propertyValue = PropertyValueConverter(rule.PropertyName, rule.PropertyValue);

            if (string.IsNullOrEmpty(propertyName)) return;

            switch (rule.Condition)
            {
                case PropertyCondition.Equal:
                    queryFilter.Add(new BoolQuery
                    {
                        Must = GetQueryType(rule, propertyName, propertyValue)
                    });

                    break;
                case PropertyCondition.NotEqual:
                    queryFilter.Add(new BoolQuery
                    {
                        MustNot = GetQueryType(rule, propertyName, propertyValue)
                    });
                    break;

                case PropertyCondition.Exists:
                    {
                        queryFilter.Add(new BoolQuery
                        {
                            Must = GetQueryType(rule, propertyName, propertyValue)
                        });
                    }
                    break;


                case PropertyCondition.NotExists:
                    {
                        queryFilter.Add(new BoolQuery
                        {
                            MustNot = GetQueryType(rule, propertyName, propertyValue)
                        });
                    }
                    break;

                default:
                    break;
            }
        }
    }

    private static QueryContainer[] GetQueryType(DecisionRule rule, string propertyName, string propertyValue)
    {
        string formattedPropertyName = rule.Formatting == PropertyFormatting.Object || rule.Formatting == PropertyFormatting.List
            ? propertyName.Replace(".keyword", "")
            : propertyName;

        if (rule.Condition == PropertyCondition.Exists)
        {
            return new QueryContainer[]
            {
                new ExistsQuery
                {
                    Field = formattedPropertyName
                }
            };
        }

        switch (rule.Formatting)
        {
            case PropertyFormatting.Object:
                return new QueryContainer[]
                    {
                        new MatchPhrasePrefixQuery
                        {
                            Field = formattedPropertyName,
                            Query = propertyValue
                        }
                    };

            case PropertyFormatting.List:
                var collection = JsonConvert.DeserializeObject<List<string>>(propertyValue);
                var queries = new List<QueryContainer>();
                foreach (var item in collection)
                {
                    queries.Add(new MatchPhrasePrefixQuery
                    {
                        Field = formattedPropertyName,
                        Query = item
                    });
                }
                return queries.ToArray();

            default:
                return new QueryContainer[]
                    {
                        new TermQuery
                        {
                            Field = propertyName,
                            Value = propertyValue
                        }
                    };
        }
    }

    private static void AddAnyFilter(DecisionRule rule, List<QueryContainer> queryFilter)
    {
        if (!anyConditions.Contains(rule.Condition)) return;

        var propertyName = PropertyNameConverter(rule.PropertyName);
        switch (rule.Condition)
        {
            case PropertyCondition.Any:
                queryFilter.Add(new BoolQuery
                {
                    Must = new QueryContainer[]
                    {
                        new TermQuery
                        {
                            Field = propertyName,
                            Value = rule.PropertyValue.ToLower()
                        }
                    }
                });
                break;
            default:
                break;
        }
    }

    private static void AddContainsFilter(DecisionRule rule, List<QueryContainer> queryFilter, int radius)
    {
        if (equalityConditions.Contains(rule.Condition) || rangeConditions.Contains(rule.Condition)) return;

        if (rule.PropertyName.StartsWith("Content:Labels:"))
        {
            AddTagContainsFilter(rule, queryFilter);
            return;
        }

        var propertyName = PropertyNameConverter(rule.PropertyName);
        if (string.IsNullOrEmpty(propertyName)) return;

        if (rule.Condition == PropertyCondition.Coordinate)
        {
            var parts = rule.PropertyValue.Split(',', StringSplitOptions.TrimEntries | StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length < 2) throw new ArgumentException("Value must be lat,lon or lat,lon,km", nameof(rule.PropertyValue));

            double userLat = double.Parse(parts[0], CultureInfo.InvariantCulture);
            double userLon = double.Parse(parts[1], CultureInfo.InvariantCulture);

            if (parts.Length > 2)
                radius = int.Parse(parts[2], CultureInfo.InvariantCulture);

            var scriptSource = @"
                if (!doc.containsKey('location') || doc['location'].empty) return false;
                double distance = doc['location'].arcDistance(params.lat, params.lon);
                return distance <= params.radiusKm * 1000;
            ";

            var script = new InlineScript(scriptSource)
            {
                Params = new Dictionary<string, object>
                {
                    ["lat"] = userLat,
                    ["lon"] = userLon,
                    ["radiusKm"] = radius
                }
            };

            queryFilter.Add(new ScriptQuery { Script = script });
            return;
        }

        var raw = rule.PropertyValue ?? string.Empty;
        if (raw.IndexOf(",") != -1 || rule.Formatting == PropertyFormatting.List)
        {
            List<string> propertyValues = rule.Formatting == PropertyFormatting.List
                ? JsonConvert.DeserializeObject<List<string>>(raw) ?? new List<string>()
                : raw.Split(",", StringSplitOptions.RemoveEmptyEntries).ToList();

            var values = propertyValues
                .Select(v => PropertyValueConverter(rule.PropertyName, v))
                .Where(v => !string.IsNullOrEmpty(v))
                .ToList();

            var final = new BoolQuery();
            var result = new BoolQuery();
            var should = values
                .Select(v => (QueryContainer)new TermQuery { Field = propertyName, Value = v })
                .ToList();

            switch (rule.Condition)
            {
                case PropertyCondition.Contains:
                    queryFilter.Add(new ScriptQuery
                    {
                        Script = new InlineScript(
                            "if (!doc.containsKey(params.field) || doc[params.field].size()==0 || doc[params.field].value == '[]') return false; for (v in params.values) if (!doc[params.field].value.toString().contains(v)) return false; return true"
                        )
                        {
                            Lang = "painless",
                            Params = new Dictionary<string, object>
                            {
                                { "field", propertyName },
                                { "values", values }
                            }
                        }
                    });
                    break;

                case PropertyCondition.ContainsIfPropertyExists:
                    queryFilter.Add(new ScriptQuery
                    {
                        Script = new InlineScript(
                            "if (!doc.containsKey(params.field) || doc[params.field].size()==0 || doc[params.field].value == '[]') return true; for (v in params.values) if (!doc[params.field].value.toString().contains(v)) return false; return true"
                        )
                        {
                            Lang = "painless",
                            Params = new Dictionary<string, object>
                            {
                                { "field", propertyName },
                                { "values", values }
                            }
                        }
                    });
                    break;

                case PropertyCondition.ContainsMinimumOne:

                    // FAIL SAFE MECHANISM IS ADDED
                    var field = propertyName;

                    var phraseShould = values
                        .Select(v => (QueryContainer)new MatchPhraseQuery
                        {
                            Field = field.Replace(".keyword", ""),
                            Query = v
                        })
                        .ToArray();

                    var phraseBool = new BoolQuery
                    {
                        Should = phraseShould,
                        MinimumShouldMatch = 1
                    };

                    var simpleQuery = new SimpleQueryStringQuery
                    {
                        Fields = new Field(field.Replace(".keyword", "")),
                        Query = string.Join(" | ", values.Select(v => $"\"{v}\"")),
                        DefaultOperator = Operator.Or
                    };

                    string scriptSource = @"if (!doc.containsKey(params.field) || doc[params.field].size()==0 || doc[params.field].value == '[]') return false; for (v in params.values) if (doc[params.field].value.toString().contains(v)) return true; return false";

                    var scriptQuery = new ScriptQuery
                    {
                        Script = new InlineScript(scriptSource)
                        {
                            Lang = "painless",
                            Params = new Dictionary<string, object>
                            {
                                { "field", propertyName },
                                { "values", values }
                            }
                        }
                    };

                    var disMax = new DisMaxQuery
                    {
                        TieBreaker = 0.0,
                        Queries = new QueryContainer[]
                        {
                            phraseBool,
                            simpleQuery,
                            scriptQuery
                        }
                    };

                    queryFilter.Add(disMax);
                    break;

                case PropertyCondition.ContainsMinimumOneIfPropertyExists:
                    queryFilter.Add(new ScriptQuery
                    {
                        Script = new InlineScript(
                            "if (!doc.containsKey(params.field) || doc[params.field].size()==0 || doc[params.field].value == '[]') return true; for (v in params.values) if (doc[params.field].value.toString().contains(v)) return true; return false"
                        )
                        {
                            Lang = "painless",
                            Params = new Dictionary<string, object>
                            {
                                { "field", propertyName },
                                { "values", values }
                            }
                        }
                    });
                    break;

                case PropertyCondition.NotContains:
                    queryFilter.Add(new ScriptQuery
                    {
                        Script = new InlineScript(
                            "if (!doc.containsKey(params.field) || doc[params.field].size()==0 || doc[params.field].value == '[]') return false; for (v in params.values) if (doc[params.field].value.toString().contains(v)) return false; return true"
                        )
                        {
                            Lang = "painless",
                            Params = new Dictionary<string, object>
                            {
                                { "field", propertyName },
                                { "values", values }
                            }
                        }
                    });
                    break;

                case PropertyCondition.NotContainsIfPropertyExists:
                    queryFilter.Add(new ScriptQuery
                    {
                        Script = new InlineScript(
                            "if (!doc.containsKey(params.field) || doc[params.field].size()==0 || doc[params.field].value == '[]') return true; for (v in params.values) if (doc[params.field].value.toString().contains(v)) return false; return true"
                        )
                        {
                            Lang = "painless",
                            Params = new Dictionary<string, object>
                            {
                                { "field", propertyName },
                                { "values", values }
                            }
                        }
                    });
                    break;
            }
        }
        else
        {
            var propertyValue = PropertyValueConverter(rule.PropertyName, raw);
            if (string.IsNullOrEmpty(propertyValue)) return;

            switch (rule.Condition)
            {
                case PropertyCondition.Contains:
                    queryFilter.Add(new ScriptQuery
                    {
                        Script = new InlineScript(
                            "if (!doc.containsKey(params.field) || doc[params.field].size()==0 || doc[params.field].value == '[]') return false; return doc[params.field].value.toString().contains(params.value)"
                        )
                        {
                            Lang = "painless",
                            Params = new Dictionary<string, object>
                        {
                            { "field", propertyName },
                            { "value", propertyValue }
                        }
                        }
                    });
                    break;

                case PropertyCondition.ContainsIfPropertyExists:
                    queryFilter.Add(new ScriptQuery
                    {
                        Script = new InlineScript(
                            "if (!doc.containsKey(params.field) || doc[params.field].size()==0 || doc[params.field].value == '[]') return true; return doc[params.field].value.toString().contains(params.value)"
                        )
                        {
                            Lang = "painless",
                            Params = new Dictionary<string, object>
                        {
                            { "field", propertyName },
                            { "value", propertyValue }
                        }
                        }
                    });
                    break;

                case PropertyCondition.ContainsMinimumOne:
                    queryFilter.Add(new BoolQuery
                    {
                        Must = new QueryContainer[]
                        {
                        new WildcardQuery { Field = propertyName, Value = $"*{propertyValue}*", CaseInsensitive = true }
                        }
                    });
                    break;

                case PropertyCondition.ContainsMinimumOneIfPropertyExists:
                    queryFilter.Add(new ScriptQuery
                    {
                        Script = new InlineScript(
                            "if (!doc.containsKey(params.field) || doc[params.field].size()==0 || doc[params.field].value == '[]') return true; return doc[params.field].value.toString().contains(params.value)"
                        )
                        {
                            Lang = "painless",
                            Params = new Dictionary<string, object>
                        {
                            { "field", propertyName },
                            { "value", propertyValue }
                        }
                        }
                    });
                    break;

                case PropertyCondition.NotContains:
                    queryFilter.Add(new BoolQuery
                    {
                        MustNot = new[]
                        {
                        (QueryContainer)new WildcardQuery { Field = propertyName, Value = $"*{propertyValue}*", CaseInsensitive = true }
                    }
                    });
                    break;

                case PropertyCondition.NotContainsIfPropertyExists:
                    queryFilter.Add(new ScriptQuery
                    {
                        Script = new InlineScript(
                            "if (!doc.containsKey(params.field) || doc[params.field].size()==0 || doc[params.field].value == '[]') return true; return !doc[params.field].value.toString().contains(params.value)"
                        )
                        {
                            Lang = "painless",
                            Params = new Dictionary<string, object>
                        {
                            { "field", propertyName },
                            { "value", propertyValue }
                        }
                        }
                    });
                    break;
            }
        }
    }





    private static void AddTagEqualityFilter(DecisionRule rule, List<QueryContainer> queryFilter)
    {
        var tagsValuesPath = $"{nameof(ContentSearchItem.Tags).ToLower()}.values.keyword";
        var tagsTypePath = $"{nameof(ContentSearchItem.Tags).ToLower()}.Type";

        if (!Enum.TryParse(rule.PropertyName.Replace("Content:Labels:", string.Empty), out LabelType type))
            return;

        var terms = new QueryContainer[]
        {
            new TermQuery
            {
                Field = tagsTypePath,
                Value = type.ToString()
            },
            new TermQuery
            {
                Field = tagsValuesPath,
                Value = rule.PropertyValue.ToLower()
            }
        };

        switch (rule.Condition)
        {
            case PropertyCondition.Equal:
                queryFilter.Add(new BoolQuery
                {
                    Must = terms
                });

                break;
            case PropertyCondition.NotEqual:
                queryFilter.Add(new BoolQuery
                {
                    MustNot = terms
                });
                break;
            case PropertyCondition.Exists:
                queryFilter.Add(new BoolQuery
                {
                    Must = new QueryContainer[]
                    {
                        new ExistsQuery
                        {
                            Field = tagsTypePath
                        }
                    }
                });
                break;

            case PropertyCondition.NotExists:
                queryFilter.Add(new BoolQuery
                {
                    MustNot = new QueryContainer[]
                    {
                        new ExistsQuery
                        {
                            Field = tagsTypePath
                        }
                    }
                });
                break;

            default:
                break;
        }
    }

    private static void AddRangeCondition(DecisionRule rule, List<QueryContainer> queryFilter)
    {
        if (!rangeConditions.Contains(rule.Condition)) return;
        if (rule.PropertyName.StartsWith("Content:Labels:")) return;
        if (rule.Formatting == PropertyFormatting.DateTime)
        {
            //rule.PropertyValue = rule.PropertyValue.Replace("T", " ");

            var propertyValue = PropertyValueConverter(rule.PropertyName, rule.PropertyValue);
            if (string.IsNullOrEmpty(propertyValue?.ToString())) return;

            string defaultDateFormat = "yyyy-MM-dd'T'HH:mm:ss";
            var dateField = $"properties.{rule.PropertyName}";

            var scriptSource = $@"
                    String f = '{dateField}';
                    if (!doc.containsKey(f) || doc[f].size() == 0) return false;

                    long eventMillis;
                    def v = doc[f].value;
                    if (v instanceof String) {{
                      def dtf = java.time.format.DateTimeFormatter.ofPattern(params.format);
                      eventMillis = java.time.LocalDateTime
                          .parse(v, dtf)
                          .atZone(java.time.ZoneId.of('UTC'))
                          .toInstant()
                          .toEpochMilli();
                    }} else {{
                      eventMillis = v.toInstant().toEpochMilli();
                    }}

                    long cutoffMillis;
                    if (params.cutoff != null && params.cutoff.length() > 0) {{
                      def dtf2 = java.time.format.DateTimeFormatter.ofPattern(params.format);
                      cutoffMillis = java.time.LocalDateTime
                          .parse(params.cutoff, dtf2)
                          .atZone(java.time.ZoneId.of('UTC'))
                          .toInstant()
                          .toEpochMilli();
                    }} else {{
                      cutoffMillis = new Date().getTime();
                    }}

                    String c = params.condition;
                    if (c == 'Greater') return cutoffMillis > eventMillis;
                    else if (c == 'GreaterOrEqual') return cutoffMillis >= eventMillis;
                    else if (c == 'Less') return cutoffMillis < eventMillis;
                    else if (c == 'LessOrEqual') return cutoffMillis <= eventMillis;
                    else return false;
                    ".Trim();

            var scriptQuery = new ScriptQuery
            {
                Script = new InlineScript(scriptSource)
                {
                    Params = new Dictionary<string, object>
                    {
                        { "cutoff", propertyValue.ToTargetDateFormat() },
                        { "format", defaultDateFormat },
                        { "condition", rule.Condition.ToString() }
                    },
                    Lang = "painless"
                }
            };

            queryFilter.Add(new BoolQuery { Must = new QueryContainer[] { scriptQuery } });

            return;
        }
        var propertyName = PropertyNameConverter(rule.PropertyName);

        if (string.IsNullOrEmpty(propertyName)) return;

        var propertyValueOther = PropertyValueConverter(rule.PropertyName, rule.PropertyValue);

        if (string.IsNullOrEmpty(propertyValueOther?.ToString())) return;

        var isNumeric = propertyName.Contains(":Stat:");

        var rangeTerm = new TermRangeQuery { Field = propertyName };

        var numericRangeTerm = new NumericRangeQuery { Field = propertyName };

        switch (rule.Condition)
        {
            case PropertyCondition.Greater:
                if (isNumeric) numericRangeTerm.GreaterThan = ParseDoubleValue(propertyValueOther);
                else rangeTerm.GreaterThan = propertyValueOther.ToString();
                break;
            case PropertyCondition.GreaterOrEqual:
                if (isNumeric) numericRangeTerm.GreaterThanOrEqualTo = ParseDoubleValue(propertyValueOther);
                else rangeTerm.GreaterThanOrEqualTo = propertyValueOther.ToString();
                break;
            case PropertyCondition.Less:
                if (isNumeric) numericRangeTerm.LessThan = ParseDoubleValue(propertyValueOther);
                else rangeTerm.LessThan = propertyValueOther.ToString();
                break;
            case PropertyCondition.LessOrEqual:
                if (isNumeric) numericRangeTerm.LessThanOrEqualTo = ParseDoubleValue(propertyValueOther);
                else rangeTerm.LessThanOrEqualTo = propertyValueOther.ToString();
                break;
            default:
                break;
        }
        queryFilter.Add(GetMandatoryFieldQuery(isNumeric ? numericRangeTerm : rangeTerm));
    }

    private static double ParseDoubleValue(object? propertyValue)
    {
        return double.TryParse(propertyValue?.ToString(), out var value) ? value : 0;
    }

    private static void AddTagContainsFilter(DecisionRule rule, List<QueryContainer> queryFilter)
    {
        var final = new BoolQuery();

        var result = new BoolQuery();
        var should = new List<QueryContainer>();

        var propertyValues = rule.PropertyValue?.Split(rule.ValueCollectionSeparator);

        propertyValues?.ForEach(x =>
        {
            var tagsValuesPath = $"{nameof(ContentSearchItem.Tags).ToLower()}.values";
            var tagsTypePath = $"{nameof(ContentSearchItem.Tags).ToLower()}.Type";

            if (!Enum.TryParse(rule.PropertyName.Replace("Content:Labels:", string.Empty), out LabelType type))
                return;

            switch (rule.Condition)
            {
                case PropertyCondition.Contains:

                    var internalBool = new BoolQuery();
                    internalBool.Must = new QueryContainer[]
                    {
                        new TermQuery
                        {
                            Field = tagsTypePath,
                            Value = type.ToString()
                        },
                        new QueryStringQuery()
                        {
                            Fields = tagsValuesPath,
                            Query = x.ToString(),
                            DefaultOperator = Operator.And
                        }
                    };

                    should.Add(internalBool);
                    break;

                default:
                    break;
            }
        });

        result.Should = should;
        result.MinimumShouldMatch = 1;

        final.Must = new QueryContainer[]
        {
            result
        };

        queryFilter.Add(final);
    }

    private static void AddTagContainsFilter(string name, string value, List<QueryContainer> queryFilter)
    {
        var tagsValuesPath = $"{nameof(ContentSearchItem.Tags).ToLower()}.values.keyword";
        var tagsTypePath = $"{nameof(ContentSearchItem.Tags).ToLower()}.Type";

        if (!Enum.TryParse(name, out LabelType type))
            return;

        var terms = new QueryContainer[]
        {
            new TermQuery
            {
                Field = tagsTypePath,
                Value = name
            },
            new TermQuery
            {
                Field = tagsValuesPath,
                Value = value.ToLower()
            }
        };

        queryFilter.Add(new BoolQuery
        {
            Must = terms
        });
    }


    private static string PropertyNameConverter(string propertyName)
    {
        if (string.IsNullOrEmpty(propertyName))
            throw new ArgumentException(
                $"ERROR: Invalid propertyName: '{propertyName}' search query will not executed");

        return propertyName switch
        {
            Constants.CONTENT_TYPE => nameof(ContentSearchItem.Type),
            Constants.CONTENT_ID => nameof(ContentSearchItem.Id),
            Constants.CONTENT_ORIGINAL_TITLE => nameof(ContentSearchItem.OriginalTitle),
            Constants.CONTENT_DURATION => nameof(ContentSearchItem.Duration),
            Constants.CONTENT_COLOR => nameof(ContentSearchItem.Color),
            Constants.CONTENT_DESCRIPTION => nameof(ContentSearchItem.Description),
            Constants.CUSTOM_ALLOW_DOWNLOAD => nameof(ContentSearchItem.Downloadable),
            Constants.CONTENT_PUBLISHEDDATE => nameof(ContentSearchItem.PublishedDate),
            Constants.CONTENT_CREATEDDATE => nameof(ContentSearchItem.CreatedDate),
            Constants.CONTENT_TITLE => nameof(ContentSearchItem.Title),
            Constants.CONTENT_VISIBILITY => nameof(ContentSearchItem.Visibility),
            Constants.CONTENT_PUBLISHED => nameof(ContentSearchItem.Published),
            Constants.CONTENT_LAST_MODIFIED => nameof(ContentSearchItem.LastModifiedDate),
            Constants.CONTENT_RELEASEDATE => nameof(ContentSearchItem.ReleaseDate),
            Constants.CONTENT_ALLOW_CHAT => nameof(ContentSearchItem.AllowChat),
            Constants.CONTENT_ALLOW_COMMENT => nameof(ContentSearchItem.AllowComments),
            Constants.CONTENT_ALLOW_REMIX => nameof(ContentSearchItem.AllowRemix),
            Constants.CONTENT_ALLOW_MINTING => nameof(ContentSearchItem.AllowMinting),
            Constants.CONTENT_ALLOW_EMAIL_NOTIFICATION => nameof(ContentSearchItem.AllowEmailNotification),
            Constants.CONTENT_ALLOW_SIDESHOW => nameof(ContentSearchItem.AllowSideshow),
            Constants.CONTENT_ALLOW_RATINGS => nameof(ContentSearchItem.AllowUserRating),
            Constants.CONTENT_ALLOW_DOWNLOAD => nameof(ContentSearchItem.Downloadable),
            Constants.CONTENT_ALLOW_LYRICS => nameof(ContentSearchItem.AllowLyrics),
            Constants.CONTENT_ALLOW_UPCOMING => nameof(ContentSearchItem.AllowUpcoming),

            Constants.CONTENT_CITIES => nameof(ContentSearchItem.Cities),
            Constants.CONTENT_COUNTRIES => nameof(ContentSearchItem.Countries),
            _ =>
                $"properties.{propertyName}" +
                //if it is a stat property we don't need to add the keyword
                (propertyName.Contains(":Stat:") || propertyName.Contains("date", StringComparison.OrdinalIgnoreCase) ? string.Empty : ".keyword")
        };
    }

    private static string? PropertyValueConverter(string propertyName, string? propertyValue)
    {
        return propertyName switch
        {
            Constants.CONTENT_TYPE => Enum.TryParse(propertyValue, out ContentType type)
                ? ((int)type).ToString()
                : ((int)ContentType.None).ToString(),
            Constants.CONTENT_DURATION => (TimeSpan.TryParse(propertyValue, out var duration)
                ? duration.TotalMilliseconds
                : TimeSpan.Zero.TotalMilliseconds).ToString("D10"),
            _ => propertyValue
        };
    }

    private static BoolQuery GetOptionalIdQuery(IEnumerable<DecisionRule> decisionRules)
    {
        var result = new BoolQuery();
        var should = new List<QueryContainer>();

        decisionRules.ForEach(rule =>
        {
            var contentIds = rule.PropertyValue.Split(rule.ValueCollectionSeparator);
            contentIds.ForEach(x =>
            {
                should.Add(new QueryStringQuery()
                {
                    Fields = $"{nameof(ContentSearchItem.Id)}",
                    Query = x,
                    Escape = true
                });
            });
        });

        result.Should = should;
        result.MinimumShouldMatch = 1;
        return result;
    }

    private static BoolQuery GetMandatoryFieldQuery(FieldNameQueryBase fieldNameQuery)
    {
        return new BoolQuery()
        {
            Must = new QueryContainer[]
            {
                fieldNameQuery
            }
        };
    }

    private static BoolQuery? GetKeywordQuery(PublicContentSearch searchArgs)
    {
        var result = new BoolQuery();
        var should = new List<QueryContainer>();
        var keyword = searchArgs.Keyword;

        should.Add(new BoolQuery
        {
            Must = new QueryContainer[]
            {
                new WildcardQuery
                {
                    CaseInsensitive = true,
                    Value = $"*{keyword}*",
                    Field = nameof(ContentSearchItem.Title)
                }
            }
        });

        should.Add(new BoolQuery
        {
            Must = new QueryContainer[]
            {
                new WildcardQuery
                {
                    CaseInsensitive = true,
                    Value = $"*{keyword}*",
                    Field = nameof(ContentSearchItem.ShortInfo)
                }
            }
        });

        should.Add(new BoolQuery
        {
            Must = new QueryContainer[]
            {
                new WildcardQuery
                {
                    CaseInsensitive = true,
                    Value = $"*{keyword}*",
                    Field = nameof(ContentSearchItem.Description)
                }
            }
        });

        result.Should = should;
        result.MinimumShouldMatch = 1;
        return result;
    }

    private static BoolQuery? GetOptionalOrganisationQuery(ContentSearch? searchArgs)
    {
        if (searchArgs == null)
            return null;

        var result = new BoolQuery();
        var should = new List<QueryContainer>();

        searchArgs.OrganizationFilters?.ForEach(x =>
        {
            var match = new MatchQuery()
            {
                Field = $"{nameof(CustomerSearchItem.Relations)}",
                Query = "Content_Organization_" + x
            };

            should.Add(match);
        });

        result.Should = should;
        result.MinimumShouldMatch = 1;
        return result;
    }

    private static BoolQuery? GetOptionalContentTypeQuery(IContentSearch searchArgs)
    {
        if (searchArgs == null)
            return null;

        var result = new BoolQuery();
        var should = new List<QueryContainer>();

        searchArgs.Types?.ForEach(x =>
        {
            var match = new MatchQuery()
            {
                Field = $"{nameof(ContentSearchItem.Type)}",
                Query = ((int)x).ToString()
            };

            should.Add(match);
        });

        result.Should = should;
        result.MinimumShouldMatch = 1;
        return result;
    }

    private static SortDescriptor<ContentSearchItem> SetupPublicSort(SortDescriptor<ContentSearchItem> sortDescriptor)
    {
        sortDescriptor.Descending(SortSpecialField.Score);
        //sortDescriptor.Field(fs => fs.Field().Order());

        return sortDescriptor;
    }

    private static SortDescriptor<ContentSearchItem> SetupSort(SortDescriptor<ContentSearchItem> sortDescriptor,
        string? sortExpr)
    {
        //first sort the result based on the query score
        sortDescriptor.Descending(SortSpecialField.Score);

        if (string.IsNullOrEmpty(sortExpr)) return sortDescriptor;

        var sortArgs = sortExpr.Split(fieldSplit, StringSplitOptions.RemoveEmptyEntries)
            .Select(str => str.Trim()).Where(str => !string.IsNullOrEmpty(str))
            .Select(str => str.Split(sortSplit, StringSplitOptions.RemoveEmptyEntries))
            .Select(p => new { field = p.First(), dir = ToDirection(p.Skip(1).FirstOrDefault()) })
            .ToList();

        foreach (var sa in sortArgs)
        {
            if (!propertyNames.TryGetValue(sa.field, out var name)) continue;
            sortDescriptor.Field(fs => fs.Field(name).Order(sa.dir));
        }

        return sortDescriptor;
    }

    private static SortDescriptor<ContentSearchItem> SetupSort(SortDescriptor<ContentSearchItem> sortDescriptor,
        OrderRules? orderRules)
    {
        if (orderRules == null || !orderRules.Any()) return sortDescriptor;

        foreach (var sa in orderRules)
            if (sa.Direction == OrderDirection.Randomize)
                sortDescriptor.Script(ssd =>
                    ssd.Script(script =>
                        script.Source($"Math.random() * 200000")
                            .Params(new Dictionary<string, object> { })
                    ).Type("number"));
            else
                sortDescriptor.Field(fs =>
                    fs.Field(PropertyNameConverter(sa.PropertyName))
                        .Order(ToDirection(sa.Direction)));

        return sortDescriptor;
    }

    private static SortOrder ToDirection(string? value)
    {
        Enum.TryParse<Direction>(value, true, out var dir);
        return dir switch
        {
            Direction.Desc => SortOrder.Descending,
            _ => SortOrder.Ascending
        };
    }

    private static SortOrder ToDirection(OrderDirection value)
    {
        return value switch
        {
            OrderDirection.Asc => SortOrder.Ascending,
            OrderDirection.Desc => SortOrder.Descending,
            _ => SortOrder.Ascending
        };
    }

    private static void AddMarkerEqualityFilter(MarkerFilter filter, List<QueryContainer> queryFilter)
    {
        var tagsValuesPath = $"{nameof(ContentSearchItem.Tags).ToLower()}.values.keyword";
        var tagsTypePath = $"{nameof(ContentSearchItem.Tags).ToLower()}.Type";

        var terms = new QueryContainer[]
        {
            new TermQuery
            {
                Field = tagsTypePath,
                Value = filter.MarkerType.ToString()
            },
            new TermQuery
            {
                Field = tagsValuesPath,
                Value = filter.Value
            }
        };

        queryFilter.Add(new BoolQuery
        {
            Must = terms
        });
    }
}

public class BoostFactors
{
    public double? IdentityBoost { get; set; } = 10000;
    public double? KeywordBoost { get; set; } = 100;
    public double? TitleBoost { get; set; } = 1000;
    public double? ShortInfoBoost { get; set; } = 30;
    public double? DescriptionBoost { get; set; } = null;
    public double? TagBoost { get; set; } = 50;
    public double? GenreBoost { get; set; } = null;
}
