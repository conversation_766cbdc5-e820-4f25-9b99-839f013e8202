﻿using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Service.OpenSearch.Implementation;
using BlueGuava.Extensions.AWS.OpenSearch;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.FeatureManagement;
using Polly;

namespace BlueGuava.ContentManagement.Service.OpenSearch;

public static class OpenSearchServiceSetup
{
    public static IServiceCollection AddContentOpenSearch(this IServiceCollection services,
        IConfiguration configuration)
    {
        // Configure the Bulkhead Policy with configurable limits
        services.AddSingleton(provider =>
        {
            var communicationSettings = configuration.GetSection("CommunicationSettings").Get<CommunicationSettings>()
                                       ?? new CommunicationSettings();

            // Create and configure the Bulkhead Policy with configurable limits
            var bulkheadPolicy = Policy.BulkheadAsync(
                communicationSettings.BulkheadMaxConcurrency,
                communicationSettings.BulkheadMaxQueueSize);

            // Return the configured Bulkhead Policy
            return bulkheadPolicy;
        });

        services.AddFeatureManagement();
        services.AddHostedService<SearchIndexInitializer>();
        services.Configure<CommunicationSettings>(configuration.GetSection("CommunicationSettings"));
        services.Configure<BoostFactors>(configuration.GetSection("SearchBoostFactors"));
        services.AddScoped<IOpenSearchService, OpenSearchService>();
        services.AddSingleton<ISearchAdapter, SearchAdapter>();
        services.AddOpenSearchClient(configuration);
        return services;
    }
}