﻿using Amazon.DynamoDBv2.DocumentModel;
using Amazon.DynamoDBv2.DataModel;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Logging;
using BlueGuava.Extensions.Logging;
using CorrelationId;
using BlueGuava.ContentManagement.Repository.DynamoDb.ContentV2.DTOs;
using System;
using BlueGuava.ContentManagement.Common.Repositories.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using Amazon.DynamoDBv2.Model;

namespace BlueGuava.ContentManagement.Repository.DynamoDb.ContentV2
{
    public partial class ContentRepository : IContentRepository
    {
        public async Task<IEnumerable<ContentPoll>> RetrievePollsByExternalIdAndReferenceId(string externalId, string referenceId)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(RetrievePollsByExternalIdAndReferenceId))
                      .Log(LogLevel.Debug, "ExternalId: {ExternalId}, ReferenceId: {ReferenceId}", externalId, referenceId);

            if (string.IsNullOrWhiteSpace(externalId) || string.IsNullOrWhiteSpace(referenceId))
                return new List<ContentPoll>();

            var cfg = new QueryOperationConfig
            {
                IndexName = ContentPollDto.ExternalIdReferenceIdIndex,
                KeyExpression = new Expression
                {
                    ExpressionStatement = "#pk = :v1 AND #sk = :v2",
                    ExpressionAttributeNames = new Dictionary<string, string>
                    {
                        { "#pk", nameof(ContentPollDto.ExternalId) },
                        { "#sk", nameof(ContentPollDto.ReferenceId) }
                    },
                    ExpressionAttributeValues = new Dictionary<string, DynamoDBEntry>
                    {
                        { ":v1", externalId },
                        { ":v2", referenceId }
                    }
                },
                Select = SelectValues.AllAttributes
            };

            var search = dynamoContext.FromQueryAsync<ContentPollDto>(cfg);
            var results = new List<ContentPollDto>();

            while (!search.IsDone)
            {
                IReadOnlyCollection<ContentPollDto>? page;
                try
                {
                    page = await search.GetNextSetAsync();
                }
                catch (Exception ex)
                {
                    logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(RetrievePollsByExternalIdAndReferenceId))
                          .Log(LogLevel.Error, ex, "Query failed");
                    break;
                }

                if (page != null && page.Count > 0)
                    results.AddRange(page);
            }

            return results?.Select(x => x.ToEntity());
        }

        public async Task<(IEnumerable<object> result, int total)> RetrievePollsByReferenceId(string referenceId)
        {
            var request = new QueryRequest
            {
                TableName = ContentPollDto.TableName,
                IndexName = ContentPollDto.ReferenceIdIndex,
                KeyConditionExpression = "#rid = :r",
                ExpressionAttributeNames = new Dictionary<string, string>
                {
                    ["#rid"] = nameof(ContentPollDto.ReferenceId),
                    ["#cn"] = nameof(ContentPollDto.CollectionName),
                    ["#sn"] = nameof(ContentPollDto.SelectedChoiceName),
                    ["#sv"] = nameof(ContentPollDto.SelectedChoiceValue)
                },
                ExpressionAttributeValues = new Dictionary<string, AttributeValue>
                {
                    [":r"] = new AttributeValue { S = referenceId }
                },
                ProjectionExpression = "#cn, #sn, #sv"
            };

            var groups = new Dictionary<string, GroupData>(StringComparer.OrdinalIgnoreCase);
            var total = 0;
            Dictionary<string, AttributeValue> lastKey = null;

            do
            {
                request.ExclusiveStartKey = lastKey;
                var resp = await dynamoDb.QueryAsync(request, default).ConfigureAwait(false);

                foreach (var item in resp.Items)
                {
                    if (!item.TryGetValue(nameof(ContentPollDto.CollectionName), out var collectionName) || string.IsNullOrEmpty(collectionName.S))
                        continue;

                    var collection = collectionName.S;
                    var choiceName = item.TryGetValue(nameof(ContentPollDto.SelectedChoiceName), out var selectedName) ? (selectedName.S ?? "N/A") : "N/A";
                    var choiceVal = item.TryGetValue(nameof(ContentPollDto.SelectedChoiceValue), out var selectedValue) ? (selectedValue.S ?? "N/A") : "N/A";

                    if (string.IsNullOrEmpty(choiceVal)) continue;

                    if (!groups.TryGetValue(collection, out var group))
                    {
                        group = new GroupData();
                        groups[collection] = group;
                    }

                    if (group.Count.TryGetValue(choiceName, out var state))
                        group.Count[choiceName] = (state.count + 1, state.id);
                    else
                        group.Count[choiceName] = (1, choiceVal);

                    group.Total++;
                    total++;
                }

                lastKey = resp.LastEvaluatedKey;
            }
            while (lastKey != null && lastKey.Count > 0);

            var result = groups.Select(s =>
            {
                var data = s.Value.Count.Select(kv => new
                {
                    Label = kv.Key,
                    Count = kv.Value.count,
                    Id = kv.Value.id,
                    Percent = s.Value.Total == 0 ? 0 : Math.Round(kv.Value.count * 100.0 / s.Value.Total, 2)
                }).ToList();

                return new
                {
                    ContentId = referenceId,
                    CollectionName = s.Key,
                    Data = data
                };
            }).ToList();

            return (result, total);
        }



        public async Task SavePoll(ContentPoll poll)
        {
            if (logger.IsEnabled(LogLevel.Debug))
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(SavePoll))
                      .Log(LogLevel.Debug, "Poll: {@Poll}", poll);

            if (poll == null) return;

            try
            {
                if (string.IsNullOrEmpty(poll.Id))
                    poll.Id = poll.GetId();
                if (poll.CreatedDate == default)
                    poll.CreatedDate = DateTime.UtcNow;

                await dynamoContext.SaveAsync(poll);
            }
            catch (Exception ex)
            {
                logger.Standards(correlationContextAccessor, nameof(ContentRepository), nameof(SavePoll))
                      .Log(LogLevel.Error, ex, "Failed to save poll item");
                throw;
            }
        }

        public async Task BatchSavePolls(IEnumerable<ContentPoll> polls)
        {
            var batch = dynamoContext.CreateBatchWrite<ContentPollDto>();
            foreach (var p in polls)
            {
                if (string.IsNullOrEmpty(p.Id)) p.Id = p.GetId();
                if (p.CreatedDate == default) p.CreatedDate = DateTime.UtcNow;
                batch.AddPutItem(ContentPollDto.FromEntity(p));
            }
            await batch.ExecuteAsync();
        }
        private class GroupData
        {
            public int Total { get; set; }
            public Dictionary<string, (int count, string id)> Count { get; } = new();
        }

    }
}
