﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Reflection.Emit;
using Amazon.DynamoDBv2.DataModel;
using Amazon.S3.Model;
using BlueGuava.ContentManagement.Common.Models;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;

namespace BlueGuava.ContentManagement.Repository.DynamoDb.ContentV2.DTOs;

[DynamoDBTable(TableName)]
public class ContentPollDto
{
    public const string ExternalIdReferenceIdIndex = "ExternalId-ReferenceId-index";
    public const string ReferenceIdIndex = "ReferenceId-index";
    public const string TableName = "Contents_V2";

    [DynamoDBHashKey] public string Id { get; set; }

    public DateTime CreatedDate { get; set; }

    [DynamoDBGlobalSecondaryIndexHashKey(ExternalIdReferenceIdIndex)]
    public string? ExternalId { get; set; }

    [DynamoDBGlobalSecondaryIndexRange<PERSON><PERSON>(ExternalIdReferenceIdIndex)]
    [DynamoDBGlobalSecondaryIndexHashKey(ReferenceIdIndex)]
    public string? ReferenceId { get; set; }

    public string? SelectedChoiceName { get; set; }
    public string? SelectedChoiceValue { get; set; }
    public string? CollectionName { get; set; }

    public string? Constituency { get; set; }
    public string? County { get; set; }
    public string? City { get; set; }
    public string? PostalCode { get; set; }

    internal ContentPoll? ToEntity()
    {
        return new ContentPoll
        {
            Id = Id,
            CreatedDate = CreatedDate,
            ExternalId = ExternalId,
            ReferenceId = ReferenceId,
            SelectedChoiceName = SelectedChoiceName,
            SelectedChoiceValue = SelectedChoiceValue,
            CollectionName = CollectionName,
            Constituency = Constituency,
            County = County,
            City = City,
            PostalCode = PostalCode
        };
    }

    internal static ContentPollDto FromEntity(ContentPoll poll)
    {
        return new ContentPollDto
        {
            Id = poll.Id,
            CreatedDate = poll.CreatedDate,
            ExternalId = poll.ExternalId,
            ReferenceId = poll.ReferenceId,
            SelectedChoiceName = poll.SelectedChoiceName,
            SelectedChoiceValue = poll.SelectedChoiceValue,
            CollectionName = poll.CollectionName,
            Constituency = poll.Constituency,
            County = poll.County,
            City = poll.City,
            PostalCode = poll.PostalCode
        };
    }
}