﻿using BlueGuava.Collections.Common;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.Extensions.AWS.Repositories.ObjectStorage.Models;
using BlueGuava.Extensions.AWS.Repositories.S3.Services;
using BlueGuava.ItemsProcessing;
using BlueGuava.NotificationService.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.Library.Common.BusinessEntities;
using Newtonsoft.Json;
using BlueGuava.Library;
using BlueGuava.ContentManagement.Common.Models;
using ClosedXML.Excel;
using BlueGuava.ContentManagement.Packages.Entities.V2;

namespace BlueGuava.ContentManagement.Service.V2
{
    public class FileGeneratorProcessor : IItemProcessor<FileGenerator>
    {
        private readonly ILogger<FileGeneratorProcessor> logger;
        private readonly IContentDynamicCatalogService catalogService;
        private readonly ICustomerService customerCacheService;
        private readonly IS3Repository s3Repository;
        private readonly INotificationService notificationService;
        private readonly IContentService contentService;
        private readonly string bucketNamePublic;
        private readonly string cdnUrl;

        public FileGeneratorProcessor(
            ILogger<FileGeneratorProcessor> logger,
            IContentDynamicCatalogService catalogService,
            ICustomerService customerCacheService,
            IS3Repository s3Repository,
            INotificationService notificationService,
            IOptionsMonitor<CdnSettings> cdnSettings,
            IOptionsMonitor<AssetSettings> bucketSettings,
            IContentService contentService,
            IConfiguration configuration)
        {
            this.logger = logger;
            this.catalogService = catalogService;
            this.customerCacheService = customerCacheService;
            this.notificationService = notificationService;
            this.s3Repository = s3Repository;
            this.contentService = contentService;

            bucketNamePublic = bucketSettings.CurrentValue.BucketName?.Contents ?? "";
            cdnUrl = cdnSettings.CurrentValue.DefaultUrl ?? configuration["Settings:CdnBaseUrl"] ?? "";
        }

        public string Name { get; } = nameof(FileGeneratorProcessor);

        public async Task Process(WorkItemContext<FileGenerator> workItemContext)
        {
            try
            {
                var item = workItemContext.WorkItem;

                logger.LogInformation("{Service} {Method} Processing item: {@Item}", nameof(FileGeneratorProcessor), nameof(Process), item);

                var mainContent = await contentService.Retrieve(item.ContentId);
                if (mainContent == null)
                {
                    logger.LogWarning("{Service} {Method} content is null: {ContentId}", nameof(FileGeneratorProcessor), nameof(Process), item.ContentId);
                    return;
                }

                var filtering = new DecisionRules
                {
                    new DecisionRule
                    {
                        PropertyName = "Content:Task:RelatedId",
                        Formatting = Library.Common.Enums.PropertyFormatting.Text,
                        PropertyValue = item.ContentId.ToString(),
                        Condition = Library.Common.Enums.PropertyCondition.Equal
                    }
                };

                if (!string.IsNullOrEmpty(item.Status))
                {
                    filtering.Add(new DecisionRule
                    {
                        PropertyName = "Content:Task:Clone:Status",
                        Formatting = Library.Common.Enums.PropertyFormatting.List,
                        PropertyValue = JsonConvert.SerializeObject(item.Status.Split(",")),
                        Condition = Library.Common.Enums.PropertyCondition.ContainsMinimumOne
                    });
                }

                var filter = JsonConvert.SerializeObject(filtering);
                var ordering = JsonConvert.SerializeObject(new OrderingGroup { LimitItem = 100000 });

                var paging = await catalogService.Search(new Common.Entities.V2.ContentSearch(), ordering.ToBase64(), filter.ToBase64(), includeOwnerId: true);

                if (paging == null || paging.Children.Count <= 0)
                {
                    logger.LogInformation("{Service} {Method} No relations found for Content: {ContentId} with Status: {Status}", nameof(FileGeneratorProcessor), nameof(Process), item.ContentId, item.Status ?? "");
                    return;
                }

                logger.LogInformation("{Service} {Method} Total relation records: {Count}", nameof(FileGeneratorProcessor), nameof(Process), paging.Children.Count);

                var customerIds = new HashSet<Guid>();

                foreach (var pageItem in paging.Children)
                {
                    if (!pageItem.Properties.TryGetValue(Constants.CONTENT_OWNER_ID, out string ownerId))
                        continue;
                    customerIds.Add(Guid.Parse(ownerId));
                }

                if (customerIds.Count == 0)
                {
                    logger.LogInformation("{Service} {Method} No target customers found for Content: {ContentId} with Status: {Status}", nameof(FileGeneratorProcessor), nameof(Process), item.ContentId, item.Status ?? "");
                    return;
                }

                logger.LogInformation("CustomerList: {Count}", customerIds.Count);

                var customers = await GetCustomers(customerIds.ToList(), default);

                if (customers.Count == 0)
                {
                    logger.LogInformation("{Service} {Method} Customers list is empty for Content: {ContentId}", nameof(FileGeneratorProcessor), nameof(Process), item.ContentId);
                    return;
                }

                logger.LogInformation("{Service} {Method} Total Customer records reterived: {Count}", nameof(FileGeneratorProcessor), nameof(Process), customers.Count);

                var bytes = BuildCustomersExcel(customers);
                var fileName = $"Customers_for_{item.ContentId}_{DateTime.UtcNow:yyyyMMddHHmmss}.xlsx";
                var s3Key = $"exports/{item.ContentId}/{fileName}";

                await s3Repository.PutFileAsync(bytes, bucketNamePublic, s3Key, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

                var link = $"{cdnUrl ?? string.Empty}/{s3Key}";

                await SendEmail(mainContent, link, Guid.Parse(item.UserId));

                logger.LogInformation("{Service} {Method} Generated and uploaded Excel for Content({ContentId}) with {Count} customers to S3 key {Key}", nameof(FileGeneratorProcessor), nameof(Process), item.ContentId, customers.Count, s3Key);
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "{Service} {Method}", nameof(FileGeneratorProcessor), nameof(Process));
            }
        }

        private async Task<List<CustomerV2Response>> GetCustomers(List<Guid> ids, CancellationToken cancel)
        {
            var results = new List<CustomerV2Response>();
            foreach (var item in ids)
            {
                var customer = await customerCacheService.Get(item);
                if (customer != null) results.Add(customer);
            }
            return results.ToList();
        }

        private byte[] BuildCustomersExcel(List<Common.Models.CustomerV2Response> customers)
        {
            using var wb = new XLWorkbook();
            var ws = wb.Worksheets.Add("Customers");

            ws.Cell(1, 1).Value = "FirstName";
            ws.Cell(1, 2).Value = "LastName";
            ws.Cell(1, 3).Value = "Email";
            ws.Cell(1, 4).Value = "County";
            ws.Cell(1, 5).Value = "OEVK";
            ws.Cell(1, 6).Value = "Volunteer";
            ws.Cell(1, 7).Value = "IslandMember";
            ws.Cell(1, 8).Value = "IslandCoordinator";
            ws.Range(1, 1, 1, 8).Style.Font.Bold = true;
            ws.SheetView.FreezeRows(1);

            var row = 2;
            foreach (var customer in customers)
            {
                if (customer == null) continue;

                ws.Cell(row, 1).Value = ReadProperty(customer, "Custom:User:First:Name", "Custom:User:Full:Name");
                ws.Cell(row, 2).Value = ReadProperty(customer, "Custom:User:Last:Name");
                ws.Cell(row, 3).Value = customer?.EmailAddress ?? ReadProperty(customer, "Custom:User:Email");
                var county = ReadProperty(customer, "Customer:Address:Current:County", "Customer:Address:Home:County");
                var oevk = ReadProperty(customer, "Customer:Address:Current:OEVK", "Customer:Address:Home:OEVK");
                ws.Cell(row, 4).Value = county;
                ws.Cell(row, 5).Value = oevk;
                ws.Cell(row, 6).Value = customer.IsVolunteer() ? "Yes" : "No";
                ws.Cell(row, 7).Value = customer.IsIslandMember() ? "Yes" : "No";
                ws.Cell(row, 8).Value = customer.IsIslandCreator() ? "Yes" : "No";
                row++;
            }

            ws.Columns().AdjustToContents(2, 60);

            using var ms = new MemoryStream();
            wb.SaveAs(ms);
            return ms.ToArray();
        }

        private string ReadProperty(CustomerV2Response customer, params string[] keys)
        {
            if (customer == null || keys == null || keys.Length == 0) return string.Empty;

            if (customer.Properties != null)
            {
                foreach (var key in keys)
                {
                    if (customer.Properties.TryGetValue(key, out var prop) && !string.IsNullOrWhiteSpace(prop))
                        return prop;
                }
            }
            return string.Empty;
        }

        private async Task SendEmail(Content content, string link, Guid userId)
        {

            var m = $"Download your customers <a href=\"" + link + $"\" target=\"_blank\">here</a>";

            var s = $"{content.OriginalTitle ?? ""} customers for download";

            var parameters = new
            {
                extraSubject = s,
                extraMessage = m
            };
            await notificationService.Trigger(MessageCenter.Common.EventName.GenericMessage, userId, parameters);
        }
    }

    public class FileGenerator
    {
        public Guid ContentId { get; set; }
        public string Status { get; set; }
        public string UserId { get; set; }
    }
}
