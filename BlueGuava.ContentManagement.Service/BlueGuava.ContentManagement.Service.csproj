﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net8.0</TargetFrameworks>
        <LangVersion>10.0</LangVersion>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="BlueGuava.Extensions.AsyncEnumerable" Version="8.1.2" />
        <PackageReference Include="BlueGuava.Extensions.AWS.EventStream.Producer" Version="8.1.12" />
        <PackageReference Include="ClosedXML" Version="0.105.0" />
        <PackageReference Include="M3U8Parser" Version="1.0.5" />
        <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
        <PackageReference Include="System.Reactive" Version="6.0.2" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\BlueGuava.ContentManagement.Common\BlueGuava.ContentManagement.Common.csproj" />
        <ProjectReference Include="..\BlueGuava.ContentManagement.Delivery\BlueGuava.ContentManagement.Delivery.csproj" />
        <ProjectReference Include="..\BlueGuava.ContentManagement.Service.EventBridge.Scheduler\BlueGuava.ContentManagement.Service.EventBridge.Scheduler.csproj" />
    </ItemGroup>

</Project>
