using BlueGuava.ContentManagement.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Polly.Bulkhead;
using Xunit;

namespace BlueGuava.ContentManagement.Service.OpenSearch.Tests;

public class BulkheadConfigurationTests
{
    [Fact]
    public void AddContentOpenSearch_ShouldConfigureBulkheadWithDefaultValues()
    {
        // Arrange
        var services = new ServiceCollection();
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string>())
            .Build();

        // Act
        services.AddContentOpenSearch(configuration);
        var serviceProvider = services.BuildServiceProvider();
        var bulkheadPolicy = serviceProvider.GetService<AsyncBulkheadPolicy>();

        // Assert
        Assert.NotNull(bulkheadPolicy);
    }

    [Fact]
    public void AddContentOpenSearch_ShouldConfigureBulkheadWithCustomValues()
    {
        // Arrange
        var services = new ServiceCollection();
        var configurationData = new Dictionary<string, string>
        {
            {"CommunicationSettings:BulkheadMaxConcurrency", "50"},
            {"CommunicationSettings:BulkheadMaxQueueSize", "1000"}
        };
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configurationData)
            .Build();

        // Act
        services.AddContentOpenSearch(configuration);
        var serviceProvider = services.BuildServiceProvider();
        var bulkheadPolicy = serviceProvider.GetService<AsyncBulkheadPolicy>();

        // Assert
        Assert.NotNull(bulkheadPolicy);
        // Note: Polly doesn't expose the configured values directly, 
        // but we can verify the policy was created successfully
    }

    [Fact]
    public void CommunicationSettings_ShouldHaveCorrectDefaultValues()
    {
        // Arrange & Act
        var settings = new CommunicationSettings();

        // Assert
        Assert.Equal(3, settings.CircuitBreakerThreshold);
        Assert.Equal(TimeSpan.FromSeconds(10), settings.CircuitBreakerDuration);
        Assert.Equal(25, settings.BulkheadMaxConcurrency);
        Assert.Equal(500, settings.BulkheadMaxQueueSize);
    }
}
