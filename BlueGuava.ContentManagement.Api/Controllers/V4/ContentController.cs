﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Runtime.Serialization;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using BlueGuava.Authorization.CloudFront;
using BlueGuava.UserLog.Client;
using BlueGuava.ContentManagement.Api.Controllers.LegacyVersions;
using BlueGuava.ContentManagement.Api.Localization.V2;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Entities.V2;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Common.Services.V2;
using BlueGuava.ContentManagement.Integration.V2;
using BlueGuava.ContentManagement.Messages.Models;
using BlueGuava.ContentManagement.Packages.Entities.Exceptions;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2.Enums;
using BlueGuava.ContentManagement.Service.V2;
using BlueGuava.Extensions.AspNetCore.Authorization.Roles;
using BlueGuava.Extensions.AspNetCore.IpAddress;
using BlueGuava.Extensions.EnumUtils;
using BlueGuava.Extensions.Logging;
using BlueGuava.HttpRepository;
using BlueGuava.ItemsProcessing;
using BlueGuava.JobManagement.Common.Entities;
using BlueGuava.JobManagement.Common.Entities.Enums;
using BlueGuava.JobManagement.Common.Requests;
using BlueGuava.JobManagement.Common.Requests.Abstraction;
using BlueGuava.JwtToken;
using BlueGuava.MessageQueuing;
using BlueGuava.OrderedSearchResult;
using CorrelationId;
using MaxMind.GeoIP2;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using LogAction = BlueGuava.UserLog.Enums.Action;
using LogComponent = BlueGuava.UserLog.Enums.Component;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;
using Microsoft.AspNetCore.Http;
using BlueGuava.Rewards.Messaging;
using static BlueGuava.ContentManagement.Common.Services.ICustomerService;
using BlueGuava.Collections.Messaging;
using BlueGuava.Collections.Common;
using BlueGuava.ContentManagement.Common.Filters;
using Microsoft.Extensions.Caching.Memory;
using BlueGuava.Library.Interop.v2;
using BlueGuava.Extensions.Paging;
using BlueGuava.NotificationService.Client;
using System.Diagnostics;

namespace BlueGuava.ContentManagement.Api.Controllers.V4;

/// <summary>
///
/// </summary>
[Route("api/v{version:apiVersion}/[controller]")]
[ApiController]
[ApiVersion("4.0")]
public partial class ContentController : ContentControllerBase
{
    private readonly IContentService contentService;
    //private readonly IContentArchival contentArchival;
    private readonly IUserLogMessagingService userLogMessagingService;
    private readonly IAutomationManagementService automationManagementService;
    private readonly IMapper mapper;
    private readonly IDynamicManifestGenerator dynamicManifestGenerator;
    private readonly IContentIngestService contentIngestService;
    private readonly IGeoIP2Provider geoIpProvider;
    private readonly IContentDynamicCatalogService contentDynamicCatalogService;
    private readonly IMessageQueue<JobRequest> jobManagerQueue;
    private readonly IHttpContextAccessor httpContextAccessor;
    private readonly IJwtTokenValidator tokenValidator;
    private readonly IMessageQueue<RewardMessage> rewardQueue;
    private readonly ICustomerService customerServiceClient;
    private readonly IMessageQueue<RelationshipUpdate> relationshipUpdate;
    private readonly IMemoryCache _memoryCache;
    private readonly IPagingContentList pagingContentList;
    protected readonly INotificationService notificationService;

    public ContentController(
        ILogger<ContentController> logger,
        IConfiguration configuration,
        IContentService contentService,
        IContentArchival contentArchival,
        IUserLogMessagingService userLogMessagingService,
        IMetricsCollector metricsCollector,
        IAutomationManagementService automationManagementService,
        ICorrelationContextAccessor correlationContextAccessor,
        IFeatureManager featureManager,
        IHttpRepositoryProvider httpRepositoryProvider,
        IMapper mapper,
        IDynamicManifestGenerator dynamicManifestGenerator,
        IContentIngestService contentIngestService,
        IGeoIP2Provider geoIpProvider,
        IOptionsMonitor<CdnSettings> cdnSettings,
        IContentDynamicCatalogService contentDynamicCatalogService,
        IItemsQueue<ReindexCommand> reindexCommandQueue,
        IMessageQueue<JobRequest> jobManagerQueue,
        IHttpContextAccessor httpContextAccessor,
        IJwtTokenValidator tokenValidator,
        IMessageQueue<RewardMessage> rewardQueue,
        ICustomerService customerServiceClient,
        IMessageQueue<RelationshipUpdate> relationshipUpdate,
        IMemoryCache memoryCache,
        IPagingContentList pagingContentList,
        INotificationService notificationService)
        : base(
            logger,
            httpRepositoryProvider,
            featureManager,
            configuration,
            correlationContextAccessor,
            contentService,
            contentArchival,
            userLogMessagingService,
            metricsCollector,
            cdnSettings
        )
    {
        this.contentService = contentService;
        //this.contentArchival = contentArchival;
        this.userLogMessagingService = userLogMessagingService;
        this.automationManagementService = automationManagementService;
        this.mapper = mapper;
        this.dynamicManifestGenerator = dynamicManifestGenerator;
        this.contentIngestService = contentIngestService;
        this.geoIpProvider = geoIpProvider;
        this.contentDynamicCatalogService = contentDynamicCatalogService;
        this.jobManagerQueue = jobManagerQueue;
        this.httpContextAccessor = httpContextAccessor;
        this.tokenValidator = tokenValidator;
        this.rewardQueue = rewardQueue;
        this.customerServiceClient = customerServiceClient;
        this.relationshipUpdate = relationshipUpdate;
        this._memoryCache = memoryCache;
        this.pagingContentList = pagingContentList;
        this.notificationService = notificationService;
    }


    [HttpGet("{contentId}")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    //[TimeCheck("Get")]
    public async Task<ActionResult<ContentResponse>> Get(Guid contentId, [FromQuery] bool? published = null)
    {
        //logger.LogInformation("ContentId: {contentId}", contentId);

        try
        {
            if (contentId == Guid.Empty) return BadRequest();
            //if (!await AuthorizedByOrganization(contentId)) return Forbid();

            var content = await contentService.Retrieve(contentId);
            if (content == null) return NotFound();

            // we're asked for published and it's not, or the other way around
            if (published.HasValue && published.Value != content.Published)
                return NotFound();

            return Ok(ConvertResult(content));
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }

    [HttpGet("{externalId}/External")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<ContentResponse>> Get(string externalId, [FromQuery] bool? published = null)
    {
        //logger.LogInformation("ExternalId: {ExternalId}", externalId);

        try
        {
            if (string.IsNullOrEmpty(externalId)) return BadRequest();

            var content = await contentService.Retrieve(externalId);
            if (content == null) return NotFound();

            //if (!await AuthorizedByOrganization(content.Id)) return Forbid();

            // we're asked for published and it's not, or the other way around
            if (published.HasValue && published.Value != content.Published)
                return NotFound();

            return Ok(ConvertResult(content));
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ExternalId: {ExternalId}", externalId);
            throw;
        }
    }

    [HttpPut("Export")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Policy = Policies.WriteAndChange)]
    public async Task<IActionResult> Export(
        [FromServices] IExternalContentManagementService extContentManagementService,
        [FromQuery] string? q = null,
        [FromQuery] ContentType? type = null,
        [FromQuery] int? minDuration = null,
        [FromQuery] int? maxDuration = null,
        [FromQuery] string? externalId = null,
        [FromQuery] string? referenceId = null,
        [FromQuery] DateTime? createdFrom = null,
        [FromQuery] DateTime? createdUntil = null,
        [FromQuery] DateTime? modifiedFrom = null,
        [FromQuery] DateTime? modifiedUntil = null,
        [FromQuery] bool? published = null,
        [FromQuery] string? cap = null,
        [FromQuery] string? cel = null,
        [FromQuery] string? cMod = null,
        [FromQuery] string? face = null,
        [FromQuery] string? emotion = null,
        [FromQuery] string? label = null,
        [FromQuery] string? genre = null,
        [FromQuery] string? category = null,
        [FromQuery] string? pT = null,
        [FromQuery] string? sub = null,
        [FromQuery] string? sortExpr = "CreatedDate DESC",
        [FromQuery] string? ownerId = null,
        [FromQuery] string? nft = null,
        [FromBody] ExportRequest? rawExportRequest = null)
    {
        const string SearchLogFormat =
            "SortExpr: {SortExpr}, SearchArgs: {@SearchArgs}";

        var markers = GenerateMarkerFilters(cap?.Split(","), cel?.Split(","), cMod?.Split(","), face?.Split(","),
            emotion?.Split(","), label?.Split(","), pT?.Split(","), sub?.Split(","), nft?.Split(","));
        var searchArgs = new ContentSearch(q, type.HasValue ? new List<ContentType>() { type.Value } : null,
            minDuration, maxDuration, externalId, referenceId, genre, category, createdFrom, createdUntil, modifiedFrom,
            modifiedUntil,
            published, markers, sortExpr, ownerId);

        //logger.LogInformation(SearchLogFormat, sortExpr, searchArgs.ToJson());

        try
        {
            var found = await extContentManagementService.RequestExport(searchArgs, rawExportRequest, User);
            if (!found) return NotFound();

            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, SearchLogFormat, sortExpr, searchArgs);
            throw;
        }
    }

    [HttpPost]
    [ProducesResponseType(201)] // Created
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<ContentResponse>> Post([FromBody] ContentRequest? request)
    {
        //logger.LogInformation("Request: {@Request}", request);

        try
        {
            if (request == null) return BadRequest();

            request.TrimCreditRecords();
            var entity = mapper.Map<Content>(request);
            //if (!await AuthorizedByOrganization(entity.Id)) return Forbid(); // to refactor in the future...

            var result = await contentService.Create(entity, User, organizations: request.Organizations!);
            if (result == null) return Conflict();

            _ = EnqueueLogEntry(LogAction.Create, null, result);

            //execute automation
            _ = automationManagementService.OnContentCreated(result, User);

            try
            {
                IncrementMetricsCounters(null, result);
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "IncrementMetricsCounter - Request: {@Request}", request);
            }

            return CreatedAtAction(nameof(Get), new { contentId = result.Id }, ConvertResult(result));
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Request: {@Request} - {message} - {stackTrace}", request, ex.Message, ex.StackTrace);

            if (ex is UnauthorizedAccessException) return Forbid(ex.Message);
            if (ex is ContentValidationException) return BadRequest(ex.Message);

            throw;
        }
    }

    [HttpPut("{contentId}")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<ContentResponse>> Put(Guid contentId, [FromBody] ContentRequest? request)
    {
        //logger.LogInformation("ContentId: {ContentId}, Request: {@Request}", contentId, request);

        try
        {
            if (contentId == Guid.Empty) return BadRequest();
            if (request == null) return BadRequest();

            var entity = await contentService.Retrieve(contentId);
            if (entity == null) return NotFound();

            if (!User.IsEditor() && !User.IsProjectPerson() &&
                User.GetCustomerId() != entity.OwnerId)
                return Forbid();

            var currentEntity = entity.ShallowCopy();
            request.TrimCreditRecords();

            var update = mapper.Map<Content>(request);
            update.Id = contentId;

            update.PublishedDate = entity.PublishedDate; // keep old value
            var wasPublished = entity.Published;

            var userId = User.GetCustomerId();
            entity.UpdateWith(update, userId != Guid.Empty ? userId : update.OwnerId, false);


            if (!entity.Published) entity.PublishedDate = null; // reset, if not 'Published'
            else if (!wasPublished) entity.PublishedDate = entity.LastModifiedDate;

            var result = await contentService.Update(entity, User);
            if (result == null) return NotFound(); // highly improbable

            await SetLifecyclePolicy(request.ArchivalPolicy, currentEntity, "lifecycle");
            await SetLifecyclePolicy(request.DeletionPolicy, currentEntity, "deletion");

            _ = EnqueueLogEntry(LogAction.Update, currentEntity, result);

            try
            {
                IncrementMetricsCounters(currentEntity, result);
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "IncrementMetricsCounters failed - ContentId: {ContentId}, Request: {@Request}",
                    contentId, request);
            }

            // generate DASH/HLS manifests on playlist update if frontend did not request it
            await dynamicManifestGenerator.TriggerManifestGeneration(result);

            return Ok(ConvertResult(result));
        }
        catch (Exception ex)
        {
            if (ex is UnauthorizedAccessException) return Forbid();
            if (ex is ContentValidationException) return BadRequest(ex.Message);
            logger.LogCritical(ex, "ContentId: {ContentId}, Request: {@Request}", contentId, request);
            throw;
        }
    }

    [HttpPost("{contentId:guid}/Copy")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<ContentResponse>> Copy(Guid contentId, [FromBody] CopyOverrides? overrides)
    {
        //logger.LogInformation("ContentId: {ContentId}", contentId);

        try
        {
            if (contentId == Guid.Empty) return BadRequest();

            //if (!await AuthorizedByOrganization(contentId)) return Forbid();

            if (User.IsTechnicalUser())
            {
                // technical user must specify in whose name it acts
                if ((overrides?.OwnerId ?? Guid.Empty) == Guid.Empty) return BadRequest();
                var claim = new Claim(ClaimTypes.NameIdentifier, overrides?.OwnerId.ToString());
                ((ClaimsIdentity)User.Identity).AddClaim(claim);
            }

            if (overrides?.OwnerId == Guid.Empty) overrides.OwnerId = null;
            if (overrides?.DefaultPrefixAndSuffix == true)
            {
                overrides.NameSuffix = $" - Copy {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}";
            }
            //overrides.Published = false; //TO-DO: make it configurable

            var result = await contentService.Copy(contentId, overrides, User, true);
            if (result == null) return Conflict(); // highly improbable

            result.Properties ??= new Dictionary<string, string>();
            if (result.Properties.ContainsKey("Content:Task:Status"))
            {
                result.Properties["Content:Task:Status"] = "Draft";
            }
            if (result.Properties.ContainsKey("Content:Task:Source"))
            {
                result.Properties["Content:Task:Source"] = "Cloned";
            }
            else
            {
                result.Properties.Add("Content:Task:Source", "Cloned");
            }
            //Content:Task:Status = Draft

            _ = EnqueueLogEntry(LogAction.Create, null, result);

            //execute automation
            _ = automationManagementService.OnContentCreated(result, User);
            try
            {
                IncrementMetricsCounters(null, result);
            }
            catch (Exception ex)
            {
                logger.Standards(correlationContextAccessor, nameof(ContentController), nameof(Copy))
                    .Log(LogLevel.Error, ex, "IncrementMetrics failed - ContentId: {ContentId}", contentId);
            }

            return CreatedAtAction(nameof(Get), new { contentId = result.Id }, ConvertResult(result));
        }
        catch (Exception ex)
        {
            if (ex is UnauthorizedAccessException) return Forbid();
            if (ex is ContentValidationException) return BadRequest(ex.Message);
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }

    [HttpPost("{contentId:guid}/Clone")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<ContentResponse>> Clone(Guid contentId, [FromBody] CopyOverrides? overrides)
    {
        //logger.LogInformation("ContentId: {ContentId}", contentId);

        try
        {
            if (contentId == Guid.Empty) return BadRequest();

            //if (!await AuthorizedByOrganization(contentId)) return Forbid();

            if (User.IsTechnicalUser())
            {
                // technical user must specify in whose name it acts
                if ((overrides?.OwnerId ?? Guid.Empty) == Guid.Empty) return BadRequest();
                var claim = new Claim(ClaimTypes.NameIdentifier, overrides?.OwnerId.ToString());
                ((ClaimsIdentity)User.Identity).AddClaim(claim);
            }

            if (overrides?.OwnerId == Guid.Empty) overrides.OwnerId = null;
            if (overrides?.DefaultPrefixAndSuffix == true)
            {
                overrides.NameSuffix = $" - Copy {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}";
            }
            //overrides.Published = false; //TO-DO: make it configurable

            var result = await contentService.Copy(contentId, overrides, User, false);
            if (result == null) return Conflict(); // highly improbable

            result.Properties ??= new Dictionary<string, string>();
            if (result.Properties.ContainsKey("Content:Task:Status"))
            {
                result.Properties["Content:Task:Status"] = "Draft";
            }
            if (result.Properties.ContainsKey("Content:Task:Source"))
            {
                result.Properties["Content:Task:Source"] = "Cloned";
            }
            else
            {
                result.Properties.Add("Content:Task:Source", "Cloned");
            }
            //Content:Task:Status = Draft

            _ = EnqueueLogEntry(LogAction.Create, null, result);

            //execute automation
            _ = automationManagementService.OnContentCreated(result, User);
            try
            {
                IncrementMetricsCounters(null, result);
            }
            catch (Exception ex)
            {
                logger.Standards(correlationContextAccessor, nameof(ContentController), nameof(Copy))
                    .Log(LogLevel.Error, ex, "IncrementMetrics failed - ContentId: {ContentId}", contentId);
            }

            return CreatedAtAction(nameof(Get), new { contentId = result.Id }, ConvertResult(result));
        }
        catch (Exception ex)
        {
            if (ex is UnauthorizedAccessException) return Forbid();
            if (ex is ContentValidationException) return BadRequest(ex.Message);
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }

    [HttpPost("{contentId}/Replace/{sourceId}")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<ContentResponse>> Replace(Guid contentId, Guid sourceId)
    {
        //logger.LogInformation("ContentId: {ContentId} SourceId {SourceId}", contentId, sourceId);

        try
        {
            if (contentId == Guid.Empty) return BadRequest("ContentId is empty");
            if (contentId == Guid.Empty) return BadRequest("SourceId is empty");

            var destination = await contentService.Retrieve(contentId);
            var source = await contentService.Retrieve(sourceId);

            if (destination == null) return NotFound($"Destination is not found:{contentId}");
            if (source == null) return NotFound($"Source is not found:{sourceId}");


            var sourceOrigin = source.Assets?.Find(x => x.SubType == SubType.Original && !x.IsDeleted);
            if (sourceOrigin == null) return NotFound($"SourceOrigin is not found:{sourceId}");

            //var destinationOrigin = destination.Assets?.Find(x => x.SubType == SubType.Original && !x.IsDeleted);
            //if (destinationOrigin == null) return NotFound($"DestinationOrigin is not found:{contentId}");

            var current = destination.ShallowCopy();


            sourceOrigin.UploaderUserId = User.GetCustomerId().ToString();
            sourceOrigin.CreatedDate = DateTime.UtcNow;

            destination.Assets ??= new List<Asset>();
            //Delete all assets
            destination.Assets.ForEach(p =>
            {
                p.IsDeleted = true;
            });

            destination.Assets.Add(sourceOrigin);
            destination.Duration = sourceOrigin.Duration;

            var result = await contentService.Update(destination, User);
            if (result == null) return Conflict();

            _ = contentIngestService.PostProcess(destination, sourceOrigin.ObjectUrl, User);

            _ = EnqueueLogEntry(LogAction.Update, current, result, "Asset replaced");

            try
            {
                IncrementMetricsCounters(current, result);
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Increment metrics failed: {@Current}", current);
            }

            return CreatedAtAction(nameof(Get), new { contentId = result.Id }, ConvertResult(result));
        }
        catch (Exception ex)
        {
            if (ex is UnauthorizedAccessException) return Forbid();
            if (ex is ContentValidationException) return BadRequest(ex.Message);
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }

    [HttpDelete("{contentId}")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult> Delete(Guid contentId, [FromQuery] bool rmLinkedAssets = false)
    {
        //logger.LogInformation("ContentId: {ContentId}", contentId);

        try
        {
            return await base.Delete(contentId);
        }
        catch (Exception ex)
        {
            if (ex is UnauthorizedAccessException) return Forbid();
            if (ex is ContentValidationException) return BadRequest(ex.Message);
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }

    [HttpDelete("{contentId}/Soft")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult> SoftDelete(Guid contentId, [FromQuery] bool rmLinkedAssets = false)
    {
        //logger.LogInformation("ContentId: {ContentId}", contentId);

        try
        {
            if (contentId == Guid.Empty) return BadRequest();

            var content = await contentService.Retrieve(contentId);
            if (content == null) return NotFound(); // no such content

            if (!User.IsEditor() && !User.IsProjectPerson() &&
                User.GetCustomerId() != content.OwnerId)
                return Forbid();

            var result = await contentService.SoftRemove(contentId, User);
            if (!result) return NotFound(); // no such content

            _ = EnqueueLogEntry(LogAction.Delete, content, null);
            try
            {
                IncrementMetricsCounters(content, null);
            }
            catch (Exception ex)
            {
                logger.Standards(correlationContextAccessor, nameof(ContentController), nameof(SoftDelete))
                    .Log(LogLevel.Error, ex, "IncrementMetrics failed - ContentId: {ContentId}", contentId);
            }

            return Ok("Deleted");
        }
        catch (Exception ex)
        {
            if (ex is UnauthorizedAccessException) return Forbid();
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }

    [HttpDelete("{contentId}/Archive/{lifecycleType}")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Policy = Policies.WriteAndChange)]
    public async Task<ActionResult> ArchiveOriginalAsset(Guid contentId, LifecycleJobType lifecycleType)
    {
        //logger.LogInformation("ContentId: {ContentId}", contentId);

        try
        {
            if (contentId == Guid.Empty) return BadRequest();
            //if (!await AuthorizedByOrganization(contentId)) return Forbid();

            var content = await contentService.Retrieve(contentId);
            if (content == null) return NotFound(); // no such content

            var originalAsset = content.Assets?.Find(x => x.SubType == SubType.Original && !x.IsDeleted);
            var lifecycle = lifecycleType >= LifecycleJobType.Delete_No ? "deletion" : "lifecycle";
            var isArchived = await contentArchival.AddArchiveTag(content.Id, originalAsset, lifecycle,
                lifecycleType.GetEnumValue(), User);

            return isArchived ? Ok("Archived") : BadRequest();
        }
        catch (Exception ex)
        {
            if (ex is UnauthorizedAccessException) return Forbid();
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }

    [HttpPut("RequestLocalization")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Policy = Policies.WriteAndChange)]
    public async Task<IActionResult> RequestLocalization([FromBody] TranslateJobRequest? request,
        [FromServices] IExternalContentManagementService extContentManagementService)
    {
        //ogger.LogInformation("JobRequest: {@JobRequest}", request);

        try
        {
            var found = await extContentManagementService.RequestLocalization(request, User);
            if (!found) return NotFound();

            _ = EnqueueTranslateStartedLogEntry(request.ToEntity(configuration));

            return Accepted();
        }
        catch (Exception ex)
        {
            if (ex is UnauthorizedAccessException) return Forbid();
            logger.LogCritical(ex, "JobRequest: {@JobRequest}", request);
            throw;
        }
    }

    [HttpPut("RequestLocalizationFile/{contentId}/{locale}")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Policy = Policies.WriteAndChange)]
    public async Task<IActionResult> RequestLocalizationFile(Guid contentId, string locale,
        [FromServices] IExternalContentManagementService extContentManagementService)
    {
        //logger.LogInformation("ContentId: {ContentId} Locale: {Locale}", contentId, locale);

        try
        {
            var (found, sourceLocale, fileLocation) =
                await extContentManagementService.PutLocalizationFileToS3(contentId, locale);
            if (!found) return NotFound();
            return Ok(new { fileLocation, sourceLocale });
        }
        catch (Exception ex)
        {
            switch (ex)
            {
                case UnauthorizedAccessException _:
                    return Forbid();
                case ArgumentException _:
                    return BadRequest(ex);
                default:
                    logger.LogCritical(ex, "ContentId: {ContentId} Locale: {Locale}", contentId, locale);
                    throw;
            }
        }
    }
    [HttpGet("{contentId}/Asset/{assetId}/Download")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest for invalid uploader format
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<IActionResult> DownloadAsset(Guid contentId, Guid assetId)
    {
        //logger.LogInformation("ContentId: {ContentId}, AssetId: {AssetId}", contentId, assetId);

        try
        {
            var ownerId = Guid.Empty;
            var uploaderId = Guid.Empty;

            var asset = await contentService.RetrieveAsset(contentId, assetId);
            if (asset == null)
            {
                return NotFound($"Asset ({assetId}) not found");
            }

            var content = await contentService.Retrieve(contentId);
            if (content == null)
            {
                return NotFound($"Content ({contentId}) not found");
            }
            ownerId = content.OwnerId;

            var currentAsset = content.Assets.Find(x => x.Id == assetId);
            if (currentAsset == null)
            {
                return NotFound($"Asset ({assetId}) not found on the content");
            }
            if (!Guid.TryParse(currentAsset.UploaderUserId, out uploaderId))
            {
                uploaderId = Guid.Empty;
            }

            /*
            if (User.GetCustomerId() != ownerId && !User.IsProjectPerson() && User.GetCustomerId() != uploaderId)
            {
                logger.LogError("User with id {UserId} does not have access to the asset OwnerId {OwnerId}, AssetUploadId: {AssetUploadId}", User.GetCustomerId(), ownerId, uploaderId);
                return Forbid();
            } */

            var publicUrl = await contentService.GeneratePublicAssetUrl(contentId, assetId);
            if (string.IsNullOrEmpty(publicUrl))
            {
                return NotFound("Public URL not generated");
            }

            return Ok(publicUrl);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ContentId: {ContentId}, AssetId: {AssetId}", contentId, assetId);
            throw;
        }
    }


    [HttpGet("{contentId}/Original")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<IActionResult> PlayerAsset(Guid contentId)
    {
        //logger.LogInformation("ContentId: {ContentId}", contentId);

        try
        {
            var content = await contentService.Retrieve(contentId);

            if (content == null) return NotFound($"Content is not found:{contentId}");

            var sourceOrigin = content.Assets?.Find(x => x.SubType == SubType.Original && !x.IsDeleted);

            if (sourceOrigin == null) return NotFound($"Original Asset is not found:{contentId}");

            Guid assetId = sourceOrigin.Id ?? Guid.Empty;

            var publicUrl = await contentService.GeneratePublicAssetUrl(contentId, assetId);

            if (string.IsNullOrEmpty(publicUrl))
                return NotFound();

            return Ok(publicUrl);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }

    [HttpPost("{contentId}/Asset/{assetId}/Reingest")]
    [ProducesResponseType(201)] // Created
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<ContentResponse>> ReingestAssetV2(Guid contentId, Guid assetId, [FromBody] IngestAssetRequestV2 ingest)
    {
        //logger.LogInformation("Asset: {Asset}", ingest.ToJson());

        try
        {
            if (ingest == null) return BadRequest();
            if (contentId == Guid.Empty) return BadRequest();
            if (string.IsNullOrEmpty(ingest.ObjectUrl)) return BadRequest();
            if (string.IsNullOrEmpty(ingest.OriginalFileName)) return BadRequest();

            var entity = await contentService.Retrieve(contentId);
            if (entity == null) return NotFound();
            var current = entity.ShallowCopy();
            ingest.UploadedByUser = true;

            var asset = ingest.ToAsset();
            asset.Id = assetId;
            asset.UploaderUserId = User.GetCustomerId().ToString();
            entity.Assets ??= new List<Asset>();
            entity.Assets.Add(asset);

            var result = await contentService.Update(entity, User);
            if (result == null) return Conflict();

            _ = EnqueueLogEntry(LogAction.Update, current, result, "Asset ingest");
            try
            {
                IncrementMetricsCounters(current, result);
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Increment metrics failed - Asset: {@Asset}", ingest);
            }

            return CreatedAtAction(nameof(Get), new { contentId = result.Id }, ConvertResult(result));
        }
        catch (Exception ex)
        {
            if (ex is UnauthorizedAccessException) return Forbid();
            if (ex is ContentValidationException) return BadRequest(ex.Message);
            logger.LogCritical(ex, "Asset: {@Asset}", ingest);
            throw;
        }
    }

    [HttpPost("{contentId}/Asset/Ingest")]
    [ProducesResponseType(201)] // Created
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<ContentResponse>> IngestAssetV2(Guid contentId, [FromBody] IngestAssetRequestV2 ingest)
    {
        //logger.LogInformation("Asset: {Asset}", ingest.ToJson());

        try
        {
            if (ingest == null) return BadRequest();
            if (contentId == Guid.Empty) return BadRequest();
            if (string.IsNullOrEmpty(ingest.ObjectUrl)) return BadRequest();
            if (string.IsNullOrEmpty(ingest.OriginalFileName)) return BadRequest();

            var entity = await contentService.Retrieve(contentId);
            if (entity == null) return NotFound();
            var current = entity.ShallowCopy();
            ingest.UploadedByUser = true;

            var asset = ingest.ToAsset();
            asset.UploaderUserId = User.GetCustomerId().ToString();
            entity.Assets ??= new List<Asset>();
            entity.Assets.Add(asset);

            var result = await contentService.Update(entity, User);
            if (result == null) return Conflict();

            _ = EnqueueLogEntry(LogAction.Update, current, result, "Asset ingest");

            try
            {
                IncrementMetricsCounters(current, result);
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Increment metrics failed - Asset: {@Asset}", ingest);
            }

            return CreatedAtAction(nameof(Get), new { contentId = result.Id }, ConvertResult(result));
        }
        catch (Exception ex)
        {
            if (ex is UnauthorizedAccessException) return Forbid();
            if (ex is ContentValidationException) return BadRequest(ex.Message);
            logger.LogCritical(ex, "Asset: {@Asset}", ingest);
            throw;
        }
    }

    [HttpPost("Asset/Ingest")]
    [ProducesResponseType(201)] // Created
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<ContentResponse>> IngestAsset([FromBody] IngestAssetRequestV1 ingest)
    {
        //logger.LogInformation("Asset: {@Asset}", ingest);

        try
        {
            if (ingest == null) return BadRequest();
            if (ingest.ContentId == Guid.Empty) return BadRequest();
            if (string.IsNullOrEmpty(ingest.ObjectUrl)) return BadRequest();
            if (string.IsNullOrEmpty(ingest.OriginalFileName)) return BadRequest();

            var entity = await contentService.Retrieve(ingest.ContentId);
            if (entity == null) return NotFound();
            var current = entity.ShallowCopy();
            ingest.UploadedByUser = true;

            var asset = ingest.ToAsset();
            asset.UploaderUserId = User.GetCustomerId().ToString();
            entity.Assets ??= new List<Asset>();

            var allowedMultipleAssetType = await featureManager.IsEnabledAsync("AllowMultipleAssetsPerSubType", true);

            if (allowedMultipleAssetType == false)
            {
                entity.Assets.RemoveAll(s => s.SubType == asset.SubType && s.Type == asset.Type && s.Locale == asset.Locale);
            }
            entity.Assets.Add(asset);

            var user = User;

            if (!string.IsNullOrEmpty(ingest.UserData))
                user = tokenValidator.GetPrincipalFromToken(ingest.UserData ?? string.Empty);

            var result = await contentService.Update(entity, user);
            if (result == null) return Conflict();

            _ = EnqueueLogEntry(LogAction.Update, current, result, "Asset ingest");

            try
            {
                IncrementMetricsCounters(current, result);
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Increment metrics failed - Asset: {@Asset}", ingest);
            }

            return CreatedAtAction(nameof(Get), new { contentId = result.Id }, ConvertResult(result));
        }
        catch (Exception ex)
        {
            if (ex is UnauthorizedAccessException) return Forbid();
            if (ex is ContentValidationException) return BadRequest(ex.Message);
            logger.LogCritical(ex, "Asset: {@Asset}", ingest);
            throw;
        }
    }

    [HttpGet("Authorize/{publicUrl}")]
    [Authorize]
    public ActionResult<string> AuthorizeAccess(string publicUrl)
    {
        publicUrl = WebUtility.UrlDecode(publicUrl);
        //logger.LogInformation("PublicUrl: {PublicUrl}", publicUrl);

        try
        {
            var builder = new UriBuilder(publicUrl);
            var contentId = Guid.TryParse(builder.Path.AsSpan(1, 36), out var id)
                ? id
                : throw new ArgumentException("Not a content URI", nameof(publicUrl));

            var lifetime = configuration.GetValue<TimeSpan>("CloudFrontToken:Lifetime");
            if (lifetime <= TimeSpan.Zero) lifetime = TimeSpan.FromDays(1);
            var expiration = DateTime.UtcNow + lifetime;

            var token = new CloudFrontToken(User.GetCustomerId(), Guid.Empty, contentId, expiration);
            var glue = string.IsNullOrEmpty(builder.Query) ? "?" : "&";
            var query = $"{glue}token={WebUtility.UrlEncode(token.Encode())}";

            builder.Query += query;
            return Ok(builder.ToString());
        }
        catch (Exception ex)
        {
            if (ex is ArgumentException ax) return ValidationProblem(ax.ParamName, ax.Message);
            logger.LogCritical(ex, "PublicUrl: {PublicUrl}", publicUrl);
            throw;
        }
    }
    /*
        [HttpGet("History/{contentId}")]
        [ProducesResponseType(200)] // OK
        [ProducesResponseType(400)] // BadRequest
        [ProducesResponseType(401)] // Unauthorized
        [ProducesResponseType(404)] // NotFound
        [ProducesResponseType(500)] // InternalServerError
        [Authorize(Policy = Policies.CreatorsWithSupportAndLecturer)]
        public async Task<ActionResult<ContentResponse>> History(Guid contentId,
            CancellationToken cancel = default)
        {
            logger.LogInformation("ContentId: {ContentId}", contentId);

            try
            {
                if (contentId == Guid.Empty) return BadRequest();

                var content = await contentService.ContentHistory(contentId, cancel);
                if (content == null) return NotFound();

                return Ok(ConvertResult(content));
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
                throw;
            }
        }
        */

    /*
    [HttpGet("Fix")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Policy = Policies.CreatorsWithSupportAndLecturer)]
    public async Task Fix([FromServices] IOpenSearchService openSearchService,
        CancellationToken cancel = default)
    {


        try
        {
            var sortExpr = "CreatedDate DESC";
            var searchArgs = new ContentSearch();
            searchArgs.CreatedFrom = new DateTime();
            searchArgs.CreatedUntil = new DateTime();

            var searchResult = await openSearchService.Search(searchArgs, 10000, 0, sortExpr, cancel);
            var result = new SearchResult<ContentResponse>(searchResult.Data.Select(ConvertResult),searchResult.TotalCount, 10000, 0);
            foreach(var item in result){

            }
            return;
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }*/

    [HttpPost("{contentId}/Reingest")]
    [ProducesResponseType(201)] // Created
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<ContentResponse>> Reingest(Guid contentId, [FromBody] IngestAssetRequestV2 ingest)
    {
        //logger.LogInformation("Reingest: {@Ingest}", ingest);

        try
        {
            if (ingest == null) return BadRequest();
            if (contentId == Guid.Empty) return BadRequest();
            if (string.IsNullOrEmpty(ingest.ObjectUrl)) return BadRequest();
            if (string.IsNullOrEmpty(ingest.OriginalFileName)) return BadRequest();

            var entity = await contentService.Retrieve(contentId);
            if (entity == null) return NotFound();
            var current = entity.ShallowCopy();
            ingest.UploadedByUser = true;

            var originalAsset = entity.Assets.FirstOrDefault(p => p.SubType == SubType.Original && !p.IsDeleted);
            if (originalAsset == null) return BadRequest("Original Asset was not found");

            originalAsset.IsDeleted = true;

            var asset = ingest.ToAsset();
            asset.Id = Guid.NewGuid();
            asset.UploaderUserId = User.GetCustomerId().ToString();
            asset.SubType = SubType.Original;
            entity.Assets ??= new List<Asset>();
            entity.Assets.Add(asset);

            var result = await contentService.Update(entity, User);

            if (result == null) return Conflict();

            _ = contentIngestService.PostProcess(entity, ingest.ObjectUrl, User);

            try
            {
                IncrementMetricsCounters(current, result);
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Increment metrics failed - Asset: {@Asset}", ingest);
            }

            return CreatedAtAction(nameof(Get), new { contentId = result.Id }, ConvertResult(result));
        }
        catch (Exception ex)
        {
            if (ex is UnauthorizedAccessException) return Forbid();
            if (ex is ContentValidationException) return BadRequest(ex.Message);
            logger.LogCritical(ex, "Asset: {@Asset}", ingest);
            throw;
        }
    }

    [HttpPost("Ingest")]
    [ProducesResponseType(201)] // Created
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<Models.ContentV2.ContentResponse>> Ingest(
        [FromBody] IngestContentRequestV2? ingest)
    {
        //logger.LogInformation("Content: {@Content}", ingest);

        try
        {
            if (ingest == null) return BadRequest();
            if (string.IsNullOrEmpty(ingest.ObjectUrl)) return BadRequest();
            if (string.IsNullOrEmpty(ingest.OriginalFileName)) return BadRequest();

            if (ingest.ContentId == Guid.Empty || !ingest.ContentId.HasValue)
                ingest.ContentId = Guid.TryParse(ingest.AssetId, out var id) ? id : Guid.NewGuid();

            var conflict = await contentService.Retrieve(ingest.ContentId ?? Guid.Empty);
            if (conflict != null) return Conflict();

            ingest.UploadedByUser = true;

            var user = User;

            if (!string.IsNullOrEmpty(ingest.UserData))
                user = tokenValidator.GetPrincipalFromToken(ingest.UserData ?? string.Empty);

            var result = await contentIngestService.Ingest(ingest, user);
            if (result == null) return Conflict();

            try
            {
                IncrementMetricsCounters(null, result, ApiMethod.Upload);
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "IncrementMetricsCounters - Content: {@Content}", ingest);
            }

            return CreatedAtAction("Get", new { contentId = result.Id, version = new ApiVersion(2, 0).ToString() },
                ConvertResult(result));
        }
        catch (Exception ex)
        {
            if (ex is UnauthorizedAccessException) return Forbid();
            if (ex is ContentValidationException) return BadRequest(ex.Message);
            logger.LogCritical(ex, "Content: {@Content}", ingest);
            throw;
        }
    }


    [HttpPost("Ingest/Copy")]
    [ProducesResponseType(201)] // Created
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult> CopyObject(string sourceBucket, string sourceKey)
    {
        try
        {
            if (string.IsNullOrEmpty(sourceBucket)) return BadRequest("No sourceBucket");
            if (string.IsNullOrEmpty(sourceKey)) return BadRequest("No sourceKey");
            if (string.IsNullOrEmpty(GetJwtToken())) return BadRequest("Jwt token is null");

            var count = await contentService.CopyObject(sourceBucket, sourceKey, User.GetCustomerId(), User.GetEmail(), GetJwtToken());
            return Ok("Processed " + count + " objects");
        }
        catch (Exception ex)
        {
            if (ex is UnauthorizedAccessException) return Forbid();
            if (ex is ContentValidationException) return BadRequest(ex.Message);
            throw;
        }
    }

    /// <summary>
    /// Search for contents in open search
    /// </summary>
    /// <returns></returns>
    [HttpGet("Search/{pageSize}/{pageIndex}")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<SearchResult<ContentResponse>>> Search(int pageSize, int pageIndex,
        [FromServices] IOpenSearchService openSearchService,
        [FromQuery] string? q,
        [FromQuery] IEnumerable<ContentType>? type,
        [FromQuery] int? minDuration,
        [FromQuery] int? maxDuration,
        [FromQuery] string? externalId = null,
        [FromQuery] string? referenceId = null,
        [FromQuery] DateTime? createdFrom = null,
        [FromQuery] DateTime? createdUntil = null,
        [FromQuery] DateTime? modifiedFrom = null,
        [FromQuery] DateTime? modifiedUntil = null,
        [FromQuery] bool? published = null,
        [FromQuery] string? cap = null,
        [FromQuery] string? cel = null,
        [FromQuery] string? cMod = null,
        [FromQuery] string? face = null,
        [FromQuery] string? emotion = null,
        [FromQuery] string? label = null,
        [FromQuery] string? genre = null,
        [FromQuery] string? category = null,
        [FromQuery] string? pT = null,
        [FromQuery] string? sub = null,
        [FromQuery] List<string>? orgs = null,
        [FromQuery] string? sortExpr = null,
        [FromQuery] string? ownerId = null,
        [FromQuery] string? nft = null,
        [FromQuery] string[]? cities = null,
        [FromQuery] string[]? countries = null,
        [FromQuery] bool clientCity = false,
        [FromQuery] double? longitude = null,
        [FromQuery] double? latitude = null,
        [FromQuery] int radius = 5,
        CancellationToken cancel = default)
    {
        const string SearchLogFormat =
            "PageSize: {PageSize}, PageIndex: {PageIndex}, SortExpr: {SortExpr}, SearchArgs: {@SearchArgs}";
        if (string.IsNullOrEmpty(q) && string.IsNullOrEmpty(sortExpr))
            sortExpr = "CreatedDate DESC";


        var markers = GenerateMarkerFilters(cap?.Split(","), cel?.Split(","), cMod?.Split(","), face?.Split(","),
        emotion?.Split(","), label?.Split(","), pT?.Split(","), sub?.Split(","), nft?.Split(","));

        if (clientCity)
        {
            var ipAddress = HttpContext?.Request?.GetClientIpAddress() ?? string.Empty;

            if (!string.IsNullOrEmpty(ipAddress))
            {
                var response = geoIpProvider.City(ipAddress);
                var city = response.City.Name ?? string.Empty;
                if (cities == null || cities.Length == 0) cities = new string[] { city };
                else cities.Append(city);
            }
        }

        var searchArgs = new ContentSearch(q, type, minDuration, maxDuration, externalId, referenceId, genre, category,
            createdFrom,
            createdUntil, modifiedFrom, modifiedUntil, published, markers, sortExpr, ownerId, cities, countries, longitude, latitude, radius);
        try
        {
            //apply from query
            searchArgs.OrganizationFilters = orgs ?? new List<string>();
            //await ApplyOrganizationFilterFromToken(searchArgs);

            logger.LogInformation(SearchLogFormat, pageSize, pageIndex, sortExpr, searchArgs);


            var searchResult = await openSearchService.Search(searchArgs, pageSize, pageIndex, sortExpr, cancel);

            return Ok(new SearchResult<ContentResponse>(searchResult.Data.Select(ConvertResult),
                searchResult.TotalCount, pageSize, pageIndex));
        }
        catch (UnauthorizedAccessException ex)
        {
            logger.LogInformation("Current user with id {currentUserId} does not have access to the content", User.GetCustomerId());
            return Unauthorized(ex.Message);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, SearchLogFormat, pageSize, pageIndex, sortExpr, searchArgs);
            throw;
        }
    }
    [HttpDelete("Batch")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult> BatchDelete([FromBody] IEnumerable<string>? contentIds)
    {
        //logger.LogInformation("ContentIds: {@ContentIds}", contentIds);

        var aggregationTask = Task.CompletedTask;

        if (contentIds == null || !contentIds.Any()) return BadRequest();

        try
        {
            var taskList = contentIds.Select(id => Delete(Guid.Parse(id)));

            aggregationTask = Task.WhenAll(taskList);

            await aggregationTask;

            return Ok("Batch deleted");
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Content DeleteBatch failed");

            if (aggregationTask.Exception?.InnerExceptions != null
                && aggregationTask.Exception.InnerExceptions.Any()
                && aggregationTask.Exception.InnerExceptions.OfType<UnauthorizedAccessException>().Any())
                return Forbid();

            throw;
        }
    }


    [HttpPut("{contentId}/Partial")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<ContentResponse>> Partial(Guid contentId,
        [FromBody] Models.ContentV2.ContentRequestPartial? request,
        [FromQuery] ListUpdateStrategy? strategy = ListUpdateStrategy.Add)
    {
        //logger.LogInformation("ContentId: {ContentId}, Content: {@Content}", contentId, request);

        try
        {
            if (contentId == Guid.Empty) return BadRequest("ContentId is Guid.Empty");
            if (request == null) return BadRequest("Request is NULL");

            var entity = await contentService.Retrieve(contentId);
            if (entity == null) return NotFound();

            if (User.GetCustomerId() != entity.OwnerId)
            {
                if (!User.IsTechnical())
                {
                    if (!User.IsEditor())
                    {
                        if (!User.IsProjectPerson())
                        {
                            logger.LogWarning("User with id {@UserId} does not have access to the content OwnerId {OwnerId}", User.GetCustomerId(), entity.OwnerId);
                            return StatusCode(StatusCodes.Status403Forbidden, "APP_NO_PERMISSION_TO_EDIT_CONTENT");
                        }
                    }
                }
            }

            var currentEntity = entity.ShallowCopy();
            request.TrimCreditRecords();

            var update = mapper.Map<Content>(request);
            update.Id = contentId;

            update.PublishedDate = entity.PublishedDate; // keep old value
            var wasPublished = entity.Published;

            var userId = User.GetCustomerId();
            entity.PartialUpdateWith(update, userId != Guid.Empty ? userId : update.OwnerId, false, (int)strategy!);

            entity.Published = request.Published ?? wasPublished;

            if (!entity.Published) entity.PublishedDate = null; // reset, if not 'Published'
            else if (!wasPublished) entity.PublishedDate = entity.LastModifiedDate;

            var result = await contentService.Update(entity, User);
            if (result == null) return NotFound(); // highly improbable

            _ = EnqueueLogEntry(LogAction.Update, currentEntity, result);

            try
            {
                IncrementMetricsCounters(currentEntity, result);
            }
            catch (Exception ex)
            {
                logger.Standards(correlationContextAccessor, nameof(ContentController), nameof(Partial))
                    .Log(LogLevel.Error, ex,
                        "IncrementMetricsCounters failed - ContentId: {ContentId}, Content: {Content}", contentId,
                        request.ToJson());
            }

            _ = Task.Run(async () =>
            {
                try
                {
                    // generate DASH/HLS manifests on playlist update
                    await dynamicManifestGenerator.GenerateManifests(result.Id.ToString());
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Failed to generate manifests for content {ContentId}", result.Id);
                }
            });

            return Ok(ConvertResult(result));
        }
        catch (Exception ex)
        {
            if (ex is UnauthorizedAccessException) return Forbid();
            logger.LogCritical(ex, "ContentId: {ContentId}, Content: {@Content}", contentId, request);
            throw;
        }
    }

    [HttpPut("{contentId:guid}/Restore")]
    [ProducesResponseType(202)] // Accepted
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(403)] // Forbidden
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Policy = Policies.WriteAndChange)]
    public async Task<ActionResult<ContentResponse>> Restore(Guid contentId)
    {
        //logger.LogInformation("Restore content with id: {Id}", contentId);

        try
        {
            if (contentId == Guid.Empty) return BadRequest();

            var entity = await contentService.Retrieve(contentId);
            if (entity == null) return NotFound();

            var originalAsset = entity.Assets?.Find(a => a.SubType == SubType.Original && !a.IsDeleted);

            if (string.IsNullOrEmpty(originalAsset?.ObjectUrl))
                return NotFound();

            if (!User.IsEditor() && !User.IsProjectPerson() && User.GetCustomerId() != entity.OwnerId)
                return Forbid();

            ContentService.ConvertUrlToS3(originalAsset.ObjectUrl, out var bucketName, out var fileKey);

            await jobManagerQueue.Enqueue(new RestoreArchivedMediaJobRequest(User.GetCustomerId(), User.GetEmail())
            {
                ReferenceObjectId = entity.Id.ToString(),
                InputFileLocation = originalAsset.ObjectUrl,
                BucketName = bucketName,
                FileKey = fileKey
            });

            return Accepted();
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Restore Exception");
            throw;
        }
    }

    /// <summary>
    /// Get Content in PropertyBag format
    /// </summary>
    /// <param name="contentId"></param>
    /// <returns></returns>
    [HttpGet("{contentId:guid}/PropertyBag")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    //[TimeCheck("PropertyBag")]
    public async Task<ActionResult<Library.Interop.v2.Object>> PropertyBag(Guid contentId)
    {
        //logger.LogInformation("ContentId: {ContentId}", contentId);
        try
        {
            var content = await contentService.PropertyBag(contentId);
            return Ok(content);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }
    [HttpGet("{contentId:guid}/PropertyBag/Cache")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    // If response headers shouldn't encourage client/proxy caching, keep it server-only:
    [ResponseCache(Duration = 60, Location = ResponseCacheLocation.Any, NoStore = false)]
    public async Task<ActionResult<Library.Interop.v2.Object>> PropertyBagCache(Guid contentId)
    {
        try
        {
            var cacheKey = $"PropertyBag:{contentId}";

            var content = await _memoryCache.GetOrCreateAsync(cacheKey, async entry =>
            {
                entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(1);

                // 👇 Add this line (decide your own size unit)
                entry.SetSize(1);

                var fetched = await contentService.PropertyBag(contentId);

                if (fetched is null)
                {
                    // Short negative-cache to avoid hammering the backend
                    entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(10);
                }

                return fetched!;
            });

            if (content is null)
                return NotFound();

            HttpContext.Response.Headers["Cache-Control"] = "public,max-age=60,s-maxage=120";
            return Ok(content);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }

    /// <summary>
    /// Get Content in PropertyBag format
    /// </summary>
    /// <param name="contentId"></param>
    /// <returns></returns>
    [HttpGet("{contentId:guid}/Cache")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<Content>> Cache(Guid contentId)
    {
        //logger.LogInformation("ContentId: {ContentId}", contentId);

        try
        {
            var content = await contentService.RetrieveFromCache(contentId);
            if (content == null) return NotFound();
            return Ok(content);
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "ContentId: {ContentId}", contentId);
            throw;
        }
    }

    [HttpPost("Approve/Batch")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Roles = "SysAdmin, Admin, TechnicalUser")]
    public async Task<ActionResult<ContentResponse>> ApproveBatch([FromBody] List<Guid> contentList)
    {
        //logger.LogInformation("id: {id}", id);

        try
        {
            if (contentList == null || !contentList.Any()) return BadRequest();

            foreach (var contentId in contentList)
            {
                var content = await contentService.Retrieve(contentId);
                if (content == null)
                {
                    logger.LogInformation("Content({ContentId}) not found", contentId);
                    continue;
                }

                if (content.IsRewarded == true)
                    continue;

                var ownerId = content.OwnerId;
                var customer = await customerServiceClient.Get(ownerId);
                if (customer == null)
                {
                    continue;
                }
                if (string.IsNullOrEmpty(content.RewardCode))
                {
                    logger.LogInformation("Content({ContentId}) has no RewardCode (RewardCode)", content.Id, content.RewardCode);
                    continue;
                }

                content.RewardCode.Split(',').ToList().ForEach(async code =>
                {
                    if (!string.IsNullOrEmpty(code))
                    {
                        logger.LogInformation("Customer({CustomerId}) has rewarded RewardCode ({RewardCode})", ownerId, content.RewardCode);
                        await rewardQueue.Enqueue(new RewardMessage { CustomerId = ownerId, RewardKey = code, ContentId = content.Id.ToString() });
                    }
                });

                var userId = User.GetCustomerId();

                var update = new Content
                {
                    Properties = new Dictionary<string, string>() {
                { "Content:Status:Date", DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss") },
                { "Content:Status", "Done" }
                        }
                };

                update.IsRewarded = true;
                content.PartialUpdateWith(update, userId != Guid.Empty ? userId : update.OwnerId, false, 2);
                await contentService.Update(content, User);
            }
            return Ok("Approved");
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Approve/Batch Exception");
            throw;
        }
    }

    [HttpGet("{contentId:guid}/Approve")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<ContentResponse>> Approve(Guid contentId)
    {
        //logger.LogInformation("id: {id}", id);

        try
        {
            var content = await contentService.Retrieve(contentId);
            if (content == null)
            {
                logger.LogInformation("Content({ContentId}) not found", contentId);

                return NotFound();
            }

            if (content.IsRewarded == true)
                return BadRequest($"Content: {contentId} is already rewarded");

            var ownerId = content.OwnerId;
            var customer = await customerServiceClient.Get(ownerId);
            if (customer == null)
            {
                return NotFound($"No customer found against OwnerId: {ownerId} of ContentId: {contentId}");
            }
            if (string.IsNullOrEmpty(content.RewardCode))
            {
                logger.LogInformation("Content({ContentId}) has no RewardCode (RewardCode)", content.Id, content.RewardCode);
                return NotFound("No reward code found");
            }

            content.RewardCode.Split(',').ToList().ForEach(async code =>
            {
                if (!string.IsNullOrEmpty(code))
                {
                    logger.LogInformation("Customer({CustomerId}) has rewarded RewardCode ({RewardCode})", ownerId, content.RewardCode);

                    var rewardMessage = new RewardMessage { CustomerId = ownerId, RewardKey = code, ContentId = content.Id.ToString() };
                    if (content.Properties?.ContainsKey("Content:Task:AutoApprove") == true && content.Properties?["Content:Task:AutoApprove"] == "true")
                    {
                        rewardMessage.SendNotification = false;
                        logger.LogInformation("Customer({CustomerId}) has rewarded ({RewardCode}) silently for ({ContentId})", ownerId, content.RewardCode, content.Id);
                    }

                    if (content.Properties?.ContainsKey("Content:Task:KnowledgeBase") == true && content.Properties?["Content:Task:KnowledgeBase"] == "true")
                    {
                        rewardMessage.AllowMultiple = false;
                        logger.LogInformation("Customer({CustomerId}) has rewarded ({RewardCode}) silently for ({ContentId})", ownerId, content.RewardCode, content.Id);
                    }

                    await rewardQueue.Enqueue(rewardMessage);
                }
            });

            var userId = User.GetCustomerId();

            var update = new Content
            {
                Properties = new Dictionary<string, string>() {
                { "Content:Status:Date", DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss") },
                { "Content:Status", "Done" }
            }
            };

            update.IsRewarded = true;

            content.PartialUpdateWith(update, userId != Guid.Empty ? userId : update.OwnerId, false, 2);

            await contentService.Update(content, User);

            return Ok("Approved");
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "id: {id}", contentId);
            throw;
        }
    }


    [HttpPost("{contentId:guid}/Feedback")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<ContentResponse>> Feedback(Guid contentId,
        Properties feedback,
        [FromQuery] string? constituency,
        [FromQuery] string? county,
        [FromQuery] string? city,
        [FromQuery] string? postalCode)
    {
        //logger.LogInformation("id: {id}", id);

        try
        {
            var content = await contentService.Retrieve(contentId);
            if (content == null)
            {
                logger.LogInformation("Content({ContentId}) not found", contentId);

                return NotFound();
            }

            if (feedback == null || feedback.Count <= 0)
            {
                logger.LogInformation("Feeback is empty for id: {id}", contentId);
                return BadRequest("APP_FEEDBACK_EMPTY");
            }

            var userId = User.GetCustomerId();

            var contentFeedback = await contentService.GetPollsByExternalIdAndReferenceId(userId.ToString(), content.Id.ToString());

            if (contentFeedback != null && contentFeedback.Any())
            {
                logger.LogInformation("CustomerId: {customerId} has already polled for  Content({ContentId}) not found", userId, contentId);
                return BadRequest("APP_CUSTOMER_ALREADY_POOLED");
            }

            var pollAnswers = new List<ContentPoll>();

            var dateTime = DateTime.Now;

            foreach (var item in feedback)
            {
                pollAnswers.Add(new ContentPoll
                {
                    CreatedDate = dateTime,
                    SelectedChoiceName = item.Name,
                    SelectedChoiceValue = item.Value,
                    CollectionName = item.CollectionName,
                    ReferenceId = content.Id.ToString(),
                    Constituency = constituency ?? "",
                    County = county ?? "",
                    City = city ?? "",
                    PostalCode = postalCode ?? ""
                });

                pollAnswers.Add(new ContentPoll
                {
                    CreatedDate = dateTime,
                    ExternalId = userId.ToString(),
                    ReferenceId = content.Id.ToString(),
                });
            }
            await contentService.BatchSavePolls(pollAnswers);

            if (string.IsNullOrEmpty(content.RewardCode))
            {
                logger.LogInformation("Content({ContentId}) has no RewardCode {RewardCode}", content.Id, content.RewardCode);
            }
            else
            {
                content.RewardCode.Split(',').ToList().ForEach(async code =>
                {
                    if (!string.IsNullOrEmpty(code))
                    {
                        logger.LogInformation("Customer({CustomerId}) has rewarded RewardCode ({RewardCode})", userId, content.RewardCode);

                        if (content.Properties?.ContainsKey("Content:Task:AutoApprove") == true && content.Properties?["Content:Task:AutoApprove"] == "true")
                        {
                            logger.LogInformation("Customer({CustomerId}) has rewarded ({RewardCode}) silently for ({ContentId})", userId, content.RewardCode, content.Id);

                            await rewardQueue.Enqueue(new RewardMessage { CustomerId = userId, RewardKey = code, ContentId = content.Id.ToString(), SendNotification = false });
                        }
                        else
                        {
                            await rewardQueue.Enqueue(new RewardMessage { CustomerId = userId, RewardKey = code, ContentId = content.Id.ToString() });
                        }
                    }
                });
            }

            return Ok("APP_POLL_ACCEPTED");
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "id: {id} Feedback: {feedback}", contentId, feedback.ToJson());
            throw;
        }
    }

    //[HttpPost("{contentId:guid}/Feedback/Approve")]
    //[ProducesResponseType(200)] // OK
    //[ProducesResponseType(400)] // BadRequest
    //[ProducesResponseType(401)] // Unauthorized
    //[ProducesResponseType(404)] // NotFound
    //[ProducesResponseType(500)] // InternalServerError
    //[Authorize]
    //public async Task<ActionResult<ContentResponse>> ApproveFeedback(Guid contentId, Properties feedback)
    //{
    //    //logger.LogInformation("id: {id}", id);

    //    try
    //    {
    //        var content = await contentService.Retrieve(contentId);
    //        if (content == null)
    //        {
    //            logger.LogInformation("Content({ContentId}) not found", contentId);

    //            return NotFound();
    //        }

    //        if (feedback == null || feedback.Count <= 0)
    //        {
    //            logger.LogInformation("Feeback is empty for id: {id}", contentId);
    //            return BadRequest("APP_FEEDBACK_EMPTY");
    //        }

    //        var userId = User.GetCustomerId();

    //        var contentFeedback = await contentService.GetPollsByExternalIdAndReferenceId(userId.ToString(), content.Id.ToString());

    //        if (contentFeedback != null && contentFeedback.Any())
    //        {
    //            logger.LogInformation("CustomerId: {customerId} has already polled for  Content({ContentId}) not found", userId, contentId);
    //            return BadRequest("APP_CUSTOMER_ALREADY_POOLED");
    //        }

    //        var pollAnswers = new List<ContentPoll>();

    //        var dateTime = DateTime.Now;

    //        foreach (var item in feedback)
    //        {
    //            pollAnswers.Add(new ContentPoll
    //            {
    //                CreatedDate = dateTime,
    //                SelectedChoiceName = item.Name,
    //                SelectedChoiceValue = item.Value,
    //                CollectionName = item.CollectionName,
    //                ExternalId = userId.ToString(),
    //                ReferenceId = content.Id.ToString()
    //            });
    //        }
    //        await contentService.BatchSavePolls(pollAnswers);

    //        if (content.IsRewarded == true)
    //            return BadRequest($"Content: {contentId} is already rewarded");

    //        var customer = await customerServiceClient.Get(userId);
    //        if (customer == null)
    //        {
    //            return NotFound($"No customer found against userId: {userId} of ContentId: {contentId}");
    //        }
    //        if (string.IsNullOrEmpty(content.RewardCode))
    //        {
    //            logger.LogInformation("Content({ContentId}) has no RewardCode {RewardCode}", content.Id, content.RewardCode);
    //            return NotFound("No reward code found");
    //        }

    //        content.RewardCode.Split(',').ToList().ForEach(async code =>
    //        {
    //            if (!string.IsNullOrEmpty(code))
    //            {
    //                logger.LogInformation("Customer({CustomerId}) has rewarded RewardCode ({RewardCode})", userId, content.RewardCode);

    //                if (content.Properties?.ContainsKey("Content:Task:AutoApprove") == true && content.Properties?["Content:Task:AutoApprove"] == "true")
    //                {
    //                    logger.LogInformation("Customer({CustomerId}) has rewarded ({RewardCode}) silently for ({ContentId})", userId, content.RewardCode, content.Id);

    //                    await rewardQueue.Enqueue(new RewardMessage { CustomerId = userId, RewardKey = code, ContentId = content.Id.ToString(), SendNotification = false });
    //                }
    //                else
    //                {
    //                    await rewardQueue.Enqueue(new RewardMessage { CustomerId = userId, RewardKey = code, ContentId = content.Id.ToString() });
    //                }
    //            }
    //        });

    //        var update = new Content
    //        {
    //            Properties = new Dictionary<string, string>() {
    //            { "Content:Status:Date", DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss") },
    //            { "Content:Status", "Done" }
    //        }
    //        };

    //        update.IsRewarded = true;

    //        content.PartialUpdateWith(update, userId != Guid.Empty ? userId : update.OwnerId, false, 2);

    //        await contentService.Update(content, User);

    //        return Ok("APP_POLL_ACCEPTED");
    //    }
    //    catch (Exception ex)
    //    {
    //        logger.LogCritical(ex, "id: {id} Feedback: {feedback}", contentId, feedback.ToJson());
    //        throw;
    //    }
    //}

    [HttpGet("{contentId:guid}/Feedback/{customerId:guid}")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize]
    public async Task<ActionResult<ContentResponse>> GetFeedback(Guid contentId, Guid customerId)
    {
        try
        {
            var content = await contentService.Retrieve(contentId);
            if (content == null)
            {
                logger.LogInformation("Content({ContentId}) not found", contentId);

                return NotFound();
            }

            var contentFeedback = await contentService.GetPollsByExternalIdAndReferenceId(customerId.ToString(), content.Id.ToString());

            if (contentFeedback == null || !contentFeedback.Any())
            {
                logger.LogInformation("CustomerId: {customerId} has not polled for Content({ContentId})", customerId.ToString(), contentId);
                return BadRequest("APP_CUSTOMER_NO_FEEDBACK");
            }

            return Ok(contentFeedback.Where(x => string.IsNullOrEmpty(x.SelectedChoiceValue)).ToList());
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "id: {id} CustomerId: {customerId}", contentId, customerId);
            throw;
        }
    }

    [HttpGet("{contentId}/Feedback/Polls/Download")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Roles = "SysAdmin, Admin, TechnicalUser")]
    public async Task<ActionResult<PagedResult<ContentPoll>>> GetPollsByContentId(string contentId)
    {
        if (string.IsNullOrWhiteSpace(contentId))
            return BadRequest("APP_REFERENCE_ID_REQ");

        var result = await pagingContentList.StartPagingByReference(contentId, 1000);
        return Ok(PagedResult.Create(result.PageContent.Where(x => !string.IsNullOrEmpty(x.SelectedChoiceValue)).ToList(), result.PagingToken));
    }

    [HttpGet("{contentId}/Feedback/Polls/{limit}")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Roles = "SysAdmin, Admin, TechnicalUser")]
    public async Task<ActionResult<PagedResult<ContentPoll>>> GetPollsByContentId(string contentId, int limit = 10)
    {
        if (string.IsNullOrWhiteSpace(contentId))
            return BadRequest("APP_REFERENCE_ID_REQ");

        var result = await pagingContentList.StartPagingByReference(contentId, limit);
        return Ok(PagedResult.Create(result.PageContent.Where(x => !string.IsNullOrEmpty(x.SelectedChoiceValue)).ToList(), result.PagingToken));
    }

    [HttpGet("{contentId}/Feedback/Polls/{limit}/{pagingToken?}")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Roles = "SysAdmin, Admin, TechnicalUser")]
    public async Task<ActionResult<PagedResult<ContentPoll>>> GetNextPollsByReference(string contentId, string pagingToken)
    {
        if (string.IsNullOrWhiteSpace(contentId))
            return BadRequest("APP_REFERENCE_ID_REQ");
        if (string.IsNullOrWhiteSpace(pagingToken))
            return BadRequest("APP_TOKEN_REQ");

        var result = await pagingContentList.GetNextPageByReference(contentId, pagingToken);
        return Ok(PagedResult.Create(result.PageContent.Where(x => !string.IsNullOrEmpty(x.SelectedChoiceValue)).ToList(), result.PagingToken));
    }

    [HttpGet("{contentId}/Feedback/Polls/Stat")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(404)]
    [ProducesResponseType(500)]
    [Authorize(Roles = "SysAdmin, Admin, TechnicalUser")]
    [TimeCheck("GetStatsByReferenceV2")]
    public async Task<ActionResult> GetStatsByReference(string contentId)
    {
        if (string.IsNullOrWhiteSpace(contentId))
            return BadRequest("ContentId is required.");
        try
        {
            var (result, total) = await contentService.GetPollsByReferenceId(contentId);
            return Ok(new { Result = result, Total = total });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in stats ContentId {contentId}", contentId);
            throw;
        }
    }


    [HttpGet("{contentId:guid}/Download/Assets")]
    [ProducesResponseType(200)] // OK
    [ProducesResponseType(400)] // BadRequest
    [ProducesResponseType(401)] // Unauthorized
    [ProducesResponseType(404)] // NotFound
    [ProducesResponseType(500)] // InternalServerError
    [Authorize(Roles = "SysAdmin, Admin, TechnicalUser")]
    public async Task<ActionResult<ContentResponse>> DownloadRelatedAssets(Guid contentId, [FromQuery] string? status)
    {
        try
        {
            var content = await contentService.Retrieve(contentId);
            if (content == null)
            {
                logger.LogInformation("Content({ContentId}) not found", contentId);

                return NotFound();
            }

            if (content.Type != ContentType.Playlist)
            {
                return BadRequest("APP_CONTENT_NOT_PLAYLIST");
            }

            await jobManagerQueue.Enqueue(new MediaProcessorJobRequest(User.GetCustomerId(), User.GetEmail())
            {
                ReferenceObjectId = content.Id.ToString(),
                MediaProcessingType = MediaProcessingType.DOWNLOAD_ASSETS,
                PlaylistId = content.Id.ToString(),
                ContentStatus = status
            });

            return Ok("APP_DOWNLOAD_APPROVED");
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "id: {id}", contentId);
            throw;
        }
    }


    [HttpGet("{contentId:guid}/Download/Excel")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(404)]
    [ProducesResponseType(408)] // Request Timeout
    [ProducesResponseType(500)]
    [Authorize(Roles = "SysAdmin, Admin, TechnicalUser")]
    public async Task<IActionResult> DownloadExcel([FromServices] IItemsQueue<FileGenerator> fileGenerator, Guid contentId, string? status, CancellationToken cancel)
    {

        try
        {
            var mainContent = await contentService.Retrieve(contentId);

            if (mainContent == null) return BadRequest("APP_CONTENT_NOT_FOUND");

            var userId = User.GetCustomerId();
            await fileGenerator.EnqueueWorkItem(new FileGenerator() { ContentId = contentId, Status = status, UserId = userId.ToString() });
            return Ok("APP_FILE_GENERATION_EXECUTED");
        }
        catch (Exception ex)
        {
            logger.LogCritical(ex, "Error while generating excel file for Content({ContentId})", contentId);
            return BadRequest("APP_FILE_GENERATION_FAILED");
        }
    }

    private ActionResult ValidationProblem(string property, string errorDetail)
    {
        ModelState.AddModelError(property, errorDetail);
        return ValidationProblem(ModelState);
    }

    private async Task EnqueueTranslateStartedLogEntry(TranslateJob updated)
    {
        var objectId = updated.Id;
        var empty = new TranslateJob { Id = null };
        await userLogMessagingService.EnqueueLogEntry(User, LogComponent.Job, LogAction.Create, objectId, empty,
            updated);
    }
    public static bool ToEnum<T>(string? str, out T result)
    {
        try
        {
            var enumType = typeof(T);
            foreach (var name in Enum.GetNames(enumType))
            {
                var enumMemberAttribute =
                    ((EnumMemberAttribute[])enumType.GetField(name)
                        .GetCustomAttributes(typeof(EnumMemberAttribute), true)).Single();
                if (enumMemberAttribute?.Value == str)
                {
                    result = (T)Enum.Parse(enumType, name);
                    return true;
                }
            }

            result = default;
            return false;
        }
        catch (Exception)
        {
            result = default;
            return false;
        }
    }

    private async Task SetLifecyclePolicy(string? lifeCyclePolicy, Content entity, string lifeCycleKey)
    {
        if (ToEnum(lifeCyclePolicy, out LifecycleJobType updateLifeCyclePolicy))
        {
            var originalAsset = entity.Assets?.Find(x => x.SubType == SubType.Original && !x.IsDeleted);

            var currentPolicy = lifeCycleKey switch
            {
                "lifecycle" => entity.ArchivalPolicy,
                "deletion" => entity.DeletionPolicy,
                _ => null
            };

            if (string.IsNullOrEmpty(currentPolicy))
            {
                await contentArchival.AddArchiveTag(entity.Id, originalAsset, lifeCycleKey,
                    updateLifeCyclePolicy.GetEnumValue(), User);
            }
            else if (ToEnum(currentPolicy, out LifecycleJobType currentLifeCyclePolicy) &&
                     updateLifeCyclePolicy != currentLifeCyclePolicy)
            {
                await contentArchival.RemoveArchiveTag(entity.Id, originalAsset, lifeCycleKey,
                    currentLifeCyclePolicy.GetEnumValue(), User);
                await contentArchival.AddArchiveTag(entity.Id, originalAsset, lifeCycleKey,
                    updateLifeCyclePolicy.GetEnumValue(), User);
            }
        }
    }

    private string? GetJwtToken()
    {
        var httpContext = httpContextAccessor.HttpContext;

        if (httpContext == null)
            return null;

        var authHeader = httpContext.Request.Headers["Authorization"].ToString();

        if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            return null;

        return authHeader.Substring("Bearer ".Length).Trim();
    }
}