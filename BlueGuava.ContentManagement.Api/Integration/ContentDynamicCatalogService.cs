using Amazon.MediaLive.Model;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Entities.V2;
using BlueGuava.ContentManagement.Common.Models;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.Extensions.Configuration.Models;
using BlueGuava.Library;
using BlueGuava.Library.Common.BusinessEntities;
using BlueGuava.Library.Common.Enums;
using BlueGuava.Library.Interop;
using BlueGuava.OrderedSearchResult;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Drawing.Printing;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace BlueGuava.ContentManagement.Api.Integration;

/// <summary>
/// Content ingest service
/// </summary>
public class ContentDynamicCatalogService : IContentDynamicCatalogService
{
    private readonly ILogger<ContentDynamicCatalogService> logger;
    private const string OBJECT_REPOSITORY_PATH = "{0}/v6/content/{1}/entity.json";
    private readonly IConfiguration configuration;
    private readonly IOpenSearchService openSearchService;
    private readonly ModuleInfo moduleInfo;
    private readonly IFeatureManager featureManager;

    public ContentDynamicCatalogService(
        ILogger<ContentDynamicCatalogService> logger,
        IOpenSearchService openSearchService,
        IOptionsMonitor<CdnSettings> cdnSettings,
        IConfiguration configuration,
        IOptions<ModuleInfo> moduleInfo,
        IFeatureManager featureManager)
    {
        this.logger = logger;
        this.openSearchService = openSearchService;
        this.moduleInfo = moduleInfo.Value;
        this.featureManager = featureManager;
        this.configuration = configuration;


    }


    /// <summary>
    /// Get dynamic catalog contents
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<Library.Interop.v2.Object?> Search(ContentSearch searchArgs,
        string orderParameter,
        string filterParameter,
        int pageIndex = 0,
        int? pageSize = null,
        bool includeOwnerId = false)
    {
        OrderingGroup ordering = new();
        DecisionRules filters = new();

        var cdnUrl = "";
        var flag = await featureManager.IsEnabledAsync("PublicCacheOff", false);

        if (flag)
        {
            cdnUrl = configuration["Original:Path:Content"];
        }
        else
        {
            cdnUrl = configuration["Settings:CdnBaseUrl"];
        }

        if (!string.IsNullOrWhiteSpace(orderParameter))
        {
            try
            {
                // Attempt to repair common issues in JSON (e.g., remove invalid characters).
                orderParameter = orderParameter.FromBase64();
                orderParameter = orderParameter.Trim();
                orderParameter = orderParameter.Replace("“", "\"") // Replace left smart quote with standard double quote
                                               .Replace("”", "\"") // Replace right smart quote with standard double quote
                                               .Replace("\\\"", "\"");  // Replace malformed sequences
                ordering = JsonConvert.DeserializeObject<OrderingGroup>(orderParameter);
            }
            catch (Exception ex)
            {
                logger.LogError("Order Parameter Parse Exception: {Ex}. Parameter: {OrderParameter}", ex, "->" + orderParameter + "<-");
                throw;
            }
        }

        if (!string.IsNullOrWhiteSpace(filterParameter))
        {
            try
            {
                // Attempt to repair common issues in JSON (e.g., remove invalid characters).
                filterParameter = filterParameter.FromBase64();
                filterParameter = filterParameter.Trim();
                filterParameter = filterParameter.Replace("“", "\"") // Replace left smart quote with standard double quote
                                                 .Replace("”", "\""); // Replace right smart quote with standard double quote;
                filters = JsonConvert.DeserializeObject<DecisionRules>(filterParameter);
            }
            catch (Exception ex)
            {
                logger.LogError("Filter Parameter Parse Exception: {Ex}. Parameter: {FilterParameter}", ex, "->" + filterParameter + "<-");
                throw;
            }
        }

        var effectivePageSize = pageSize > 0 ? pageSize.Value : (ordering.LimitItem > 0 ? ordering.LimitItem : 200);

        var searchResult = await openSearchService.Search(searchArgs, filters, new OrderRules(ordering?.OrderRules), effectivePageSize, pageIndex, default);

        var result = new Library.Interop.v2.Object();
        var index = 0;
        if (searchResult.Hits?.Data != null)
        {
            foreach (var item in searchResult.Hits.Data)
            {
                var child = new Library.Interop.v2.Object();
                child.Actions.SetAction(new Library.Interop.v2.Action()
                {
                    Url = flag ? string.Format(cdnUrl, item.Id) : string.Format(OBJECT_REPOSITORY_PATH, $"{cdnUrl}/api", item.Id),
                    HttpMethod = "GET",
                    ActionName = "Navigation"
                });
                child.SetProperty(Constants.CONTENT_ID, item.Id);
                child.SetProperty(Constants.CONTENT_TYPE, "Content");
                child.SetProperty(Constants.CONTENT_INDEX, index++);
                if(includeOwnerId)
                    child.SetProperty(Constants.CONTENT_OWNER_ID, item.OwnerId);

                result.AddChild(child);
            }
        }

        result.SetProperty("TotalCount", searchResult.Hits?.TotalCount ?? 0);
        result.Version = moduleInfo.Version;
        result.TimeStamp = DateTime.UtcNow.ToString("o");
        return result;
    }

    public async Task<int> Count(
    ContentSearch searchArgs,
    string orderingGroup,
    string filter,
    int pageIndex = 0,
    int? pageSize = null)
    {
        OrderingGroup ordering = new();
        DecisionRules filters = new();

        if (!string.IsNullOrWhiteSpace(orderingGroup))
        {
            try
            {
                orderingGroup = orderingGroup.FromBase64().Trim()
                                             .Replace("“", "\"").Replace("”", "\"");
                ordering = JsonConvert.DeserializeObject<OrderingGroup>(orderingGroup);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Order Parameter Parse Exception: {OrderingGroup}", orderingGroup);
                throw;
            }
        }

        if (!string.IsNullOrWhiteSpace(filter))
        {
            try
            {
                filter = filter.FromBase64().Trim()
                               .Replace("“", "\"").Replace("”", "\"");
                filters = JsonConvert.DeserializeObject<DecisionRules>(filter);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Filter Parameter Parse Exception: {Filter}", filter);
                throw;
            }
        }

        var totalCount = await openSearchService.Count(
            searchArgs,
            filters,
            new OrderRules(ordering?.OrderRules),
            default
        );

        return totalCount;
    }

    /// <summary>
    /// Get dynamic catalog contents
    /// </summary>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<(SearchResult<Content>? Hits, IReadOnlyDictionary<string, Dictionary<string, long>> GroupCounts)> Query(ContentSearch searchArgs,
        string orderParameter,
        string filterParameter,
        int pageIndex = 0,
        int? pageSize = null,
        string groupBy = null)
    {
        OrderingGroup ordering = new();
        DecisionRules filters = new();
        List<string> groupByList = new();

        var cdnUrl = "";
        var flag = await featureManager.IsEnabledAsync("PublicCacheOff", false);

        if (flag)
        {
            cdnUrl = configuration["Original:Path:Content"];
        }
        else
        {
            cdnUrl = configuration["Settings:CdnBaseUrl"];
        }

        if (!string.IsNullOrWhiteSpace(orderParameter))
        {
            try
            {
                // Attempt to repair common issues in JSON (e.g., remove invalid characters).
                orderParameter = orderParameter.FromBase64();
                orderParameter = orderParameter.Trim();
                orderParameter = orderParameter.Replace("“", "\"") // Replace left smart quote with standard double quote
                                               .Replace("”", "\"") // Replace right smart quote with standard double quote
                                               .Replace("\\\"", "\"");  // Replace malformed sequences
                ordering = JsonConvert.DeserializeObject<OrderingGroup>(orderParameter);
            }
            catch (Exception ex)
            {
                logger.LogError("Order Parameter Parse Exception: {Ex}. Parameter: {OrderParameter}", ex, "->" + orderParameter + "<-");
                throw;
            }
        }

        if (!string.IsNullOrWhiteSpace(filterParameter))
        {
            try
            {
                // Attempt to repair common issues in JSON (e.g., remove invalid characters).
                filterParameter = filterParameter.FromBase64();
                filterParameter = filterParameter.Trim();
                filterParameter = filterParameter.Replace("“", "\"") // Replace left smart quote with standard double quote
                                                 .Replace("”", "\""); // Replace right smart quote with standard double quote;
                filters = JsonConvert.DeserializeObject<DecisionRules>(filterParameter);
            }
            catch (Exception ex)
            {
                logger.LogError("Filter Parameter Parse Exception: {Ex}. Parameter: {FilterParameter}", ex, "->" + filterParameter + "<-");
                throw;
            }
        }

        if (!string.IsNullOrWhiteSpace(groupBy))
        {
            try
            {
                // Attempt to repair common issues in JSON (e.g., remove invalid characters).
                groupBy = groupBy.FromBase64();
                groupBy = groupBy.Trim();
                groupBy = groupBy.Replace("“", "\"") // Replace left smart quote with standard double quote
                                                 .Replace("”", "\""); // Replace right smart quote with standard double quote;
                groupByList = JsonConvert.DeserializeObject<List<string>>(groupBy);
            }
            catch (Exception ex)
            {
                logger.LogError("Group by Parameter Parse Exception: {Ex}. Parameter: {GroupBy}", ex, "->" + groupBy + "<-");
                throw;
            }
        }

        var effectivePageSize = pageSize > 0 ? pageSize.Value : (ordering.LimitItem > 0 ? ordering.LimitItem : 200);

        var searchResult = await openSearchService.Search(searchArgs, filters, new OrderRules(ordering?.OrderRules), effectivePageSize, pageIndex, default, groupByList);

        return searchResult;
    }

    public async Task<Library.Interop.v2.Object?> QueryScript(string scriptId,
        IDictionary<string, object> parameters,
        int pageSize = 20,
        int pageIndex = 0,
        CancellationToken cancel = default)
    {

        var searchResult = await openSearchService.ExecuteMustache(scriptId, parameters, pageSize, pageIndex, cancel);

        var result = new Library.Interop.v2.Object();
        var index = 0;

        var cdnUrl = "";
        var flag = await featureManager.IsEnabledAsync("PublicCacheOff", false);

        if (flag)
        {
            cdnUrl = configuration["Original:Path:Content"];
        }
        else
        {
            cdnUrl = configuration["Settings:CdnBaseUrl"];
        }

        if (searchResult?.Data != null)
        {
            foreach (var item in searchResult.Data)
            {
                var child = new Library.Interop.v2.Object();
                child.Actions.SetAction(new Library.Interop.v2.Action()
                {
                    Url = flag ? string.Format(cdnUrl, item.Id) : string.Format(OBJECT_REPOSITORY_PATH, $"{cdnUrl}/api", item.Id),
                    HttpMethod = "GET",
                    ActionName = "Navigation"
                });

                string category = string.Empty;

                item?.Properties?.TryGetValue("Content:Task:Category", out category);

                child.SetProperty(Constants.CONTENT_ID, item.Id);
                child.SetProperty("Content:Task:Category", category ?? "");
                child.SetProperty(Constants.CONTENT_TYPE, "Content");
                child.SetProperty(Constants.CONTENT_INDEX, index++);

                result.AddChild(child);
            }
        }

        result.SetProperty("TotalCount", searchResult?.TotalCount ?? 0);
        result.Version = moduleInfo.Version;
        result.TimeStamp = DateTime.UtcNow.ToString("o");
        return result;
    }

    public async Task SyncMustacheScriptsFromFile()
    {
        await openSearchService.SyncMustacheScriptsFromFile();
    }

    public async Task<GroupPageResult> QueryGroupsOnly(
    ContentSearch searchArgs,
    string orderParameter,
    string filterParameter,
    int pageIndex = 0,
    int pageSize = 50,
    string groupBy = null)
    {
        OrderingGroup ordering = new();
        DecisionRules filters = new();
        List<string> groupByList = new();

        if (!string.IsNullOrWhiteSpace(orderParameter))
        {
            orderParameter = orderParameter.FromBase64().Trim()
                .Replace("“", "\"").Replace("”", "\"").Replace("\\\"", "\"");
            ordering = JsonConvert.DeserializeObject<OrderingGroup>(orderParameter);
        }

        if (!string.IsNullOrWhiteSpace(filterParameter))
        {
            filterParameter = filterParameter.FromBase64().Trim()
                .Replace("“", "\"").Replace("”", "\"");
            filters = JsonConvert.DeserializeObject<DecisionRules>(filterParameter);
        }

        if (!string.IsNullOrWhiteSpace(groupBy))
        {
            groupBy = groupBy.FromBase64().Trim()
                .Replace("“", "\"").Replace("”", "\"");
            groupByList = JsonConvert.DeserializeObject<List<string>>(groupBy) ?? new();
        }

        return await openSearchService.SearchGroupsOnly(searchArgs, filters, new OrderRules(ordering?.OrderRules), pageSize, pageIndex, default, groupByList);
    }


    private DecisionRules FromEntity(List<CatalogDecisionRule> entity)
    {
        return new DecisionRules(entity.Select(FromEntity));
    }

    private DecisionRule FromEntity(CatalogDecisionRule entity)
    {
        return new DecisionRule
        {
            Index = entity.Index,
            PropertyName = entity.PropertyName,
            Formatting = FromEntity(entity.Formatting),
            Condition = FromEntity(entity.Condition),
            PropertyValue = entity.PropertyValue,
            ValueIsReference = entity.ValueIsReference,
            ValueCollectionSeparator = entity.ValueCollectionSeparator ?? '\0',
            PropertyCollectionSeparator = entity.PropertyCollectionSeparator ?? '\0',
            ContainsCondition = FromEntity(entity.ContainsCondition)
        };
    }

    private PropertyFormatting FromEntity(ValueFormat value)
    {
        return (PropertyFormatting)(int)value;
    }

    private PropertyCondition FromEntity(ConditionName value)
    {
        return (PropertyCondition)(int)value;
    }

    private StringMatching FromEntity(CatalogStringMatching value)
    {
        return (StringMatching)(int)value;
    }

    private OrderDirection FromEntity(CatalogSortDirection value)
    {
        return (OrderDirection)(int)value;
    }

    private Properties FromEntity(List<CatalogProperty> entity)
    {
        return new Properties(entity.Select(FromEntity));
    }

    private static T? NullIfEmpty<T>(T? collection)
        where T : class, System.Collections.ICollection
    {
        return collection?.Count > 0 ? collection : null;
    }

    private Property FromEntity(CatalogProperty entity)
    {
        return new Property
        {
            CollectionName = entity.Collection,
            Name = entity.Name,
            Value = entity.Value
        };
    }

    private OrderRules FromEntity(List<CatalogOrderRule> entity)
    {
        return new OrderRules(entity.Select(FromEntity));
    }

    private OrderRule FromEntity(CatalogOrderRule entity)
    {
        return new OrderRule
        {
            Index = entity.Index,
            PropertyName = entity.PropertyName,
            Direction = FromEntity(entity.Direction),
            Formatting = FromEntity(entity.ValueFormat),
            ValueOrder = NullIfEmpty(entity.ValueList)
        };
    }
}