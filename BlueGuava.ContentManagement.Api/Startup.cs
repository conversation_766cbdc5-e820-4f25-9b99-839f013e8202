using System;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Reflection;
using Amazon;
using Amazon.DynamoDBv2.DataModel;
using Amazon.SimpleNotificationService;
using BlueGuava.Collections.Messaging;
using BlueGuava.ContentManagement.Api.Caching;
using BlueGuava.ContentManagement.Api.Infrastructure;
using BlueGuava.ContentManagement.Api.Integration;
using BlueGuava.ContentManagement.Api.Localization.V2;
using BlueGuava.ContentManagement.Api.Models;
using BlueGuava.ContentManagement.Api.Monitoring;
using BlueGuava.ContentManagement.Api.Queuing;
using BlueGuava.ContentManagement.Api.Validation;
using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Models;
using BlueGuava.ContentManagement.Common.Monitoring;
using BlueGuava.ContentManagement.Common.Services.V3;
using BlueGuava.ContentManagement.Delivery;
using BlueGuava.ContentManagement.Delivery.ItemProcessors;
using BlueGuava.ContentManagement.Delivery.Models.Items;
using BlueGuava.ContentManagement.Delivery.V3;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Messages.Models;
using BlueGuava.ContentManagement.Service.AmazonIVS.EventBridge;
using BlueGuava.ContentManagement.Service.Automation;
using BlueGuava.ContentManagement.Service.OpenSearch;
using BlueGuava.ContentManagement.Delivery.Models;
using BlueGuava.ContentManagement.Packages.Entities.Validation;
using BlueGuava.ContentManagement.Repository.DynamoDb.ContentV2;
using BlueGuava.ContentManagement.Service.AmazonIVS;
using BlueGuava.ContentManagement.Service.Chime;
using BlueGuava.ContentManagement.Service.MediaLive;
using BlueGuava.ContentManagement.Service.V2;
using BlueGuava.ContentManagement.Service.EventBridge.Scheduler;
using BlueGuava.ContentManagement.Api.Queuing.Model;
using BlueGuava.ContentManagement.Common.Repositories.V2;
using BlueGuava.ContentManagement.Service;
using BlueGuava.ContentManagement.Messages.Entities;


using BlueGuava.Extensions.Configuration.Models;
using BlueGuava.HttpRepository;
using BlueGuava.JobManagement.Common.Entities;
using BlueGuava.JobManagement.Common.Requests;
using BlueGuava.JobManagement.Common.Requests.Abstraction;
using BlueGuava.Library.Delivery.Messaging.Models;
using BlueGuava.Reporting.Messages.Entities;
using BlueGuava.Reporting.Messages.Entities.WriteBacks;
using BlueGuava.Rewards.Messaging;
using BlueGuava.Webhook.Messaging.Entities;
using CorrelationId;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Prometheus;
using BlueGuava.EventStream.Common.Model.Classification.Content;
using BlueGuava.Extensions.AspNetCore.Authentication;
using BlueGuava.Extensions.AspNetCore.Authorization.Roles;
//using BlueGuava.Extensions.AspNetCore.BrandIdVerification;
using BlueGuava.Extensions.AspNetCore.ExceptionHandling;
using BlueGuava.Extensions.AWS.EventStream.Producer.DependencyInjection;
using BlueGuava.Extensions.AWS.MaxMindGeoIp.DependencyInjection;
using BlueGuava.Extensions.AWS.MessageQueuing.Amazon.DependencyInjection;
using BlueGuava.Extensions.AWS.Repositories.S3;
using BlueGuava.Extensions.Configuration;
using BlueGuava.Extensions.InMemory.DependencyInjection;
using BlueGuava.Extensions.Swagger.ApiVersioning;
using BlueGuava.HealthReporting;
using BlueGuava.MarkerManagement.Models;
using BlueGuava.NotificationService.Client;
using BlueGuava.Tracewind.Common;
using Microsoft.AspNetCore.ResponseCompression;
using Polly.Extensions.Http;
using Polly;
using System.Net.Http;
using Amazon.Scheduler;
using Amazon.SecurityToken;

using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.FeatureManagement;
using Prometheus.HttpMetrics;
using static BlueGuava.ContentManagement.Common.Services.ICustomerService;
using BlueGuava.Extensions.AWS.Repositories.ObjectStorage.DependencyInjection;
using BlueGuava.Extensions.AWS.Repositories.ObjectStorage.Interfaces;
using BlueGuava.Extensions.AWS.Repositories.ObjectStorage;


namespace BlueGuava.ContentManagement.Api;

/// <summary>
/// Startup class
/// </summary>
public class Startup
{
    private const string CorsPolicy = "CorsPolicy";

    /// <summary>
    ///
    /// </summary>
    /// <param name="configuration"></param>
    public Startup(IConfiguration configuration)
    {
        Configuration = configuration;
    }

    /// <summary>
    /// Gets the configuration
    /// </summary>
    public IConfiguration Configuration { get; }

    /// <summary>
    /// This method gets called by the runtime. Use this method to add services to the container
    /// </summary>
    /// <param name="services"></param>
    public void ConfigureServices(IServiceCollection services)
    {
        services.Configure<AuthenticationSettings>(Configuration.GetSection("Settings"));
        services.Configure<JwtToken.JwtSettings>(Configuration.GetSection("jwt"));
        services.Configure<KestrelServerOptions>(Configuration.GetSection("Kestrel"));
        services.Configure<S3Bucket>(Configuration.GetSection("S3Bucket"));
        services.Configure<CdnSettings>(Configuration.GetSection("CdnSettings"));
        services.Configure<ModuleInfo>(Configuration.GetSection("ModuleInfo"));
        services.Configure<ItemSequenceOptions>(Configuration.GetSection("ItemSequenceOptions"));
        services.Configure<ContentReleaseOption>(Configuration.GetSection("Content:Release"));

        services.AddControllers(opts =>
            {
                opts.Filters.Add<CustomErrorFilter>();

                opts.CacheProfiles.Add(CacheProfiles.OneMinute,
                    new CacheProfile()
                    {
                        Duration = 60 // one minute
                    });

                opts.CacheProfiles.Add(CacheProfiles.FiveMinutes,
                    new CacheProfile()
                    {
                        Duration = 5 * 60 // five minutes
                    });

                opts.CacheProfiles.Add(CacheProfiles.OneHour,
                    new CacheProfile()
                    {
                        Duration = 60 * 60 // One hour
                    });

                opts.CacheProfiles.Add(CacheProfiles.DynamicCatalog,
                    new CacheProfile()
                    {
                        Duration = Convert.ToInt32(Configuration["DynamicCatalogCacheTimingInSeconds"] ?? "60") // Configureable
                    });


                opts.CacheProfiles.Add(CacheProfiles.FiveSeconds,
                    new CacheProfile()
                    {
                        Duration = 5
                    });
            })
            .AddNewtonsoftJson(opts =>
            {
                opts.SerializerSettings.DefaultValueHandling = DefaultValueHandling.Ignore;
                opts.SerializerSettings.NullValueHandling = NullValueHandling.Ignore;
                opts.SerializerSettings.Converters.Add(new StringEnumConverter());
            });

        services.AddCors(options =>
        {
            options.AddPolicy(CorsPolicy,
                builder => builder
                    .AllowAnyOrigin()
                    .AllowAnyMethod()
                    .AllowAnyHeader());
        });

        services.AddSwaggerGenNewtonsoftSupport();
        services.AddSwaggerGen(opts =>
        {
            opts.SwaggerDoc("v4.0", new OpenApiInfo { Title = "ENT.360 - Content Service", Version = "v4.0" });
            opts.SwaggerDoc("v1.0", new OpenApiInfo { Title = "ENT.360 - Content Interop Service", Version = "v1.0" });
            opts.AddAwsApiGatewayRole(Configuration["Roles:Lambda:ContentManagement"]);
            opts.AddBearerTokenRequirement();
            opts.CustomSchemaIds(x => x.FullName);
            opts.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory,
                $"{Assembly.GetExecutingAssembly().GetName().Name}.xml"));
        });

        services.AddVersioning();

        services.AddDefaultAWSOptions(Configuration.GetAWSOptions());

        // ✅ Use Concrete Implementations for AWS Services
        services.AddAWSService<Amazon.DynamoDBv2.IAmazonDynamoDB>();
        services.AddTransient<IDynamoDBContext, DynamoDBContext>();
        services.AddAWSService<IAmazonSimpleNotificationService>();
        services.AddAWSService<IAmazonScheduler>();
        services.AddAWSService<IAmazonSecurityTokenService>();

        services.AddTransient<BlueGuava.ContentManagement.Common.Services.IAmazonIvsService, BlueGuava.ContentManagement.Service.AmazonIVS.Implementation.AmazonIvsService>();

        services.AddMemoryCache(options =>
        {
            // Set memory cache limits to prevent OOM
            options.SizeLimit = 100_000; // Limit to 100k entries
            options.CompactionPercentage = 0.25; // Remove 25% when limit reached
        });
        services.AddLockedInMemoryCache();

        services.ConfigureRepositoryS3(); // setup s3 repository

        //initialize GeoIp service
        services.AddAmazonS3(Configuration)
            .AddS3MaxMindGeoIpProvider(Configuration.GetSection("MaxMindGeoIp"))
            .AddAmazonS3MaxMind(Configuration);

        services.AddBrand();
        services.AddCorrelationId();
        services.AddHttpContextAccessor();
        services.AddUserLogMessagingService(Configuration);

        services.AddAmazonSQS(Configuration)
            .AddAmazonMessageQueue<RewardMessage>("Reward")
            .AddAmazonMessageQueue<Job>("MarkerJobRequests")
            .AddAmazonMessageQueue<JobRequest>("JobRequests")
            .AddAmazonMessageQueue<WebHookEvent>("WebhookEvents")
            .AddAmazonMessageQueue<JobUpdate>("JobUpdates.fifo")
            .AddAmazonMessageQueue<UseCaseRequest>("UseCaseRequests")
            .AddAmazonMessageQueue<CommercialMetricMessage>("CommercialMetricMessages")
            .AddAmazonMessageQueue<ContentCreationMetricMessage>("ContentCreationMetricMessages")
            .AddAmazonMessageQueue<PlatformMetricMessage>("PlatformMetricMessages")
            .AddAmazonMessageQueue<DiscussionUpdate>("DiscussionUpdates")
            //.AddAmazonMessageQueue<PersistContentMessage>("PersistMessages.fifo")
            .AddAmazonMessageQueue<ContentWriteBackMessage>("ContentWriteBackMessages")
            .AddMessageQueueWorker(Configuration)
            .ForItemProcessor<ContentWriteBackProcessor>()
            .AddAmazonMessageQueue<RelationshipUpdate>("RelationshipUpdates")
            .ConfigureSerializer(opts => opts.Converters.Add(new RelationshipJsonConverter()))
            .AddAmazonMessageQueue<RelationshipUpdateAll>("RelationshipUpdatesAll")
            .ConfigureSerializer(opts => opts.Converters.Add(new RelationshipJsonConverter()))
            .AddAmazonMessageQueue<UpdateMessage>("ContentUpdates")
            .AddMessageQueueWorker(Configuration)
            .ForItemProcessor<ContentUpdateMessageProcessor>()
            .AddAmazonMessageQueue<IvsCallbackEvent>("IVSCallbackEvents")
            .AddMessageQueueWorker(Configuration)
            .ForItemProcessor<IvsCallbackEventProcessor>()
            .AddAmazonMessageQueue<MediaLiveControlTask>("MediaLiveControlTasks")
            .AddMessageQueueWorker(Configuration)
            .ForItemProcessor<MediaLiveControlTaskProcessor>()
            .AddAmazonMessageQueue<MediaLiveStreamJob>("MediaLiveStreamJobs")
            .AddMessageQueueWorker(Configuration)
            .ForItemProcessor<MediaLiveStreamJobProcessor>()
            .AddAmazonMessageQueue<ObjectReleaseMessage>("CatalogToContent.fifo")
            .AddMessageQueueWorker(Configuration)
            .ForItemProcessor<ObjectReleaseMessageProcessor>()
            .AddAmazonMessageQueue<ContentIngestMessage>("ContentIngest")
            .AddMessageQueueWorker(Configuration)
            .ForItemProcessor<ContentIngestProcessor>()
            .AddAmazonMessageQueue<InteropObjectMessage>("CatalogReleases.fifo")
            .AddAmazonMessageQueue<ContentPublishMessage>("ContentPublisher.fifo")
            .AddMessageQueueWorker(Configuration)
            .ForItemProcessor<ContentSchedulerProcessor>()
            .AddAmazonMessageQueue<ResourceCheckTrigger>("ResourceCheckTriggers")
            .AddMessageQueueWorker(Configuration)
            .ForItemProcessor<ResourceCheckProcessor>()
            .AddAmazonMessageQueue<AdvertismentTrigger>("AdvertisementTriggers.fifo")
            .AddMessageQueueWorker(Configuration)
            .ForItemProcessor<AdvertisementTriggerProcessor>()
            .AddAmazonMessageQueue<MarkerMessage>("MarkerUpdates")
            .ConfigureSerializer(settings => settings.TypeNameHandling = TypeNameHandling.Auto)
            .AddAmazonMessageQueue<JoinProjectMessage>("JoinProjectMessages")
            .AddMessageQueueWorker(Configuration)
            .ForItemProcessor<JoinProjectMessageProcessor>()
            .AddAmazonMessageQueue<ChatMessage>("ChatMessages")
            .AddMessageQueueWorker(Configuration).ForItemProcessor<ChatMessageProcessor>()
            .AddTraceLogQueue(Configuration).AddTracewindServiceJobManagement();

        services.AddItemsChannel<ReindexCommand>(Configuration)
            .AddItemProcessor<ReindexRunner>();

        services.AddItemsChannel<FileGenerator>(Configuration)
            .AddItemProcessor<FileGeneratorProcessor>();

        services
            .AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddBlueGuavaJwtBearer(Configuration);

        services.InitializeSupportAuthorizationPolicies();
        services.AddNotificationService(Configuration);
        services.AddScoped<IMessagingService, MessagingService>();
        //services.Configure<BrandConfig>(Configuration.GetSection("Settings"));
        services.Configure<Globalization>(Configuration.GetSection("Globalization"));

        services.AddScoped<IContentValidator, ContentValidator>();
        services.Configure<ValidationOptions>(Configuration.GetSection("ContentValidation"));

        services.AddScoped<Common.Services.V2.IContentMaintenance, ContentMaintenance>();
        services.AddScoped<Common.Services.V2.IContentService, ContentService>();
        services.AddScoped<Common.Services.V2.IContentArchival, ContentArchival>();
        services.AddScoped<Common.Services.V2.IContentExport, ContentExport>();
        services.AddScoped<Common.Services.V2.IAutomationManagementService, AutomationManagementService>();
        services.AddScoped<IContentIntegration, ContentIntegrationService>();
        services.AddScoped<IMediaLiveControl, MediaLiveControlService>();
        services.AddScoped<IContentIngestService, ContentIngestService>();
        services.AddScoped<IContentDynamicCatalogService, ContentDynamicCatalogService>();
        services.AddScoped<IDynamicManifestGenerator, DynamicManifestGenerator>();
        services.AddScoped<IEventBridgeSchedulerService, EventBridgeSchedulerService>();
        services.AddTransient<ICustomerService, CustomerServiceClient>();
        services.AddTransient<IMediaHelperService, MediaHelperServiceClient>();
        services.AddCachingObjectProvider(Configuration);
        services.AddScoped<IObjectProvider, CachingObjectProvider>(); // override object provider

        services.AddContentOpenSearch(Configuration);
        services.AddChimeIntegration(Configuration);
        services.AddAmazonIVSIntegration(Configuration);
        services.AddMediaLiveIntegration(Configuration);

        services.AddScoped<IExternalContentManagementService, ExternalContentManagementService>();

        services.AddScoped<IContentRepository, ContentRepository>();

        services.AddScoped<Common.Services.V2.IPagingContentList, PagingContentList>();
        services.AddScoped<IPagingContentRepository, PagingContentRepository>();
        services.AddDataProducer<DataContract>();
        services
            .AddSingleton<IObjectSequenceProcessor<Library.Interop.v2.Object>, InteropObjectSequencePayloadProcessor>();

        services.AddSingleton<IMetricsCollector, PrometheusMetrics>();
        services.AddHostedService<MetricsInitializer>();
        services.AddHostedService<InteropObjectSequenceInitializer>();

        services.AddHttpRepository(Configuration, ServiceNames.Collections);
        services.AddHttpRepository(Configuration, "CountryManagement");
        services.AddHttpRepository(Configuration, "ImageProcessor");
        services.AddHttpRepository(Configuration, ServiceNames.AppModelManagement);
        services.AddHttpRepository(Configuration, ServiceNames.JobManager);
        services.AddHttpRepository(Configuration, ServiceNames.MediaHelper);

        services.AddConverters();
        services.AddAutoMapper(AppDomain.CurrentDomain.GetAssemblies());
        services.AddScoped<IDeliveryService, ContentDeliveryService>();
        services.AddScoped<IContentTagService, ContentTagService>();

        services.AddHttpAppModelRepository(Configuration.GetSection("AppModelService"));

        services.AddItemsChannel<ContentReleaseTrigger>(Configuration)
            .AddItemProcessor<ContentDeliveryProcessor>();
        services.AddItemsChannel<ContentTag>(Configuration.GetSection("ContentTagProcessor"))
            .AddItemProcessor<ContentTagProcessor>();

        services.AddItemsChannel<ManualCatalogItem>(Configuration.GetSection("ManualCatalogItemProcessor"))
            .AddItemProcessor<ManualCatalogItemProcessor>();
        services.AddItemsChannel<SmartCatalogItem>(Configuration.GetSection("SmartCatalogItemProcessor"))
            .AddItemProcessor<SmartCatalogItemProcessor>();

        services.AddTransient<IImageProcessorService, ImageProcessorService>();
        services.AddTransient<ICollectionServiceClient, CollectionServiceClient>();
        services.AddTransient<IJobManager, JobManagerClient>();
        services.AddScoped<JwtToken.IJwtTokenValidator, JwtToken.JwtTokenValidator>();

        services.AddHttpClient<IOpenStreetMapService, OpenStreetMapService>(client =>
            {
                client.BaseAddress = new Uri("http://nominatim.openstreetmap.org/reverse");
            })
            .AddPolicyHandler(GetRetryPolicy())
            .AddPolicyHandler(GetCircuitBreakerPolicy());

        services.AddOutputCache(options =>
        {
            options.AddPolicy("DefaultCachingPolicy", cachePolicyBuilder => cachePolicyBuilder.AddPolicy<DefaultCachingPolicy>(), true);
        });
        services.AddResponseCompression(options =>
        {
            options.EnableForHttps = true;
            options.Providers.Add<BrotliCompressionProvider>();
            options.Providers.Add<GzipCompressionProvider>();
            options.MimeTypes = ResponseCompressionDefaults.MimeTypes.Concat(new[] { "image/svg+xml" });
        });



        services.Configure<BrotliCompressionProviderOptions>(options => { options.Level = CompressionLevel.Fastest; });

        services.Configure<GzipCompressionProviderOptions>(options =>
        {
            options.Level = CompressionLevel.SmallestSize;
        });

        services.AddHttpClient<IManifestDownloader, ManifestDownloader>();


        services.AddHealthChecks()
            .AddCheck<HealthCheck<IContentRepository>>("ContentRepository", HealthStatus.Degraded)
            .AddCheck<HealthCheck<ISearchAdapter>>("SearchAdapter", HealthStatus.Degraded)
            .ForwardToPrometheus();


        // add a publisher to cache the latest health report
        services.AddPassiveHealthCheck(Configuration);
    }

    /// <summary>
    /// This method gets called by the runtime. Use this method to configure the HTTP request pipeline
    /// </summary>
    /// <param name="app"></param>
    /// <param name="env"></param>
    /// <param name="provider"></param>
    /// <summary>
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IApiVersionDescriptionProvider provider)
    {

        // Enable response compression
        app.UseResponseCompression();

        app.UseExceptionMiddleware();

        // Apply CORS policy
        app.UseCors(CorsPolicy);

        // Add security headers
        app.Use(async (context, next) =>
        {
            context.Response.Headers.Add("X-SV", Configuration["ModuleInfo:Version"]);

            // Content Security Policy - Helps prevent XSS attacks
            context.Response.Headers.Add("Content-Security-Policy",
                "default-src 'self'; " +
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
                "style-src 'self' 'unsafe-inline'; " +
                "img-src 'self' data: https:; " +
                "font-src 'self' data:; " +
                "connect-src 'self'; " +
                "frame-ancestors 'none'");

            // X-Frame-Options - Prevents clickjacking attacks
            context.Response.Headers.Add("X-Frame-Options", "DENY");

            // Referrer Policy - Controls referrer information
            context.Response.Headers.Add("Referrer-Policy", "strict-origin-when-cross-origin");

            // Permissions Policy - Controls browser features
            context.Response.Headers.Add("Permissions-Policy",
                "camera=(), " +
                "microphone=(), " +
                "geolocation=(), " +
                "payment=(), " +
                "usb=(), " +
                "magnetometer=(), " +
                "gyroscope=(), " +
                "accelerometer=()");

            // Strict Transport Security - Enforces HTTPS (only add in production)
            if (!env.IsDevelopment())
            {
                context.Response.Headers.Add("Strict-Transport-Security",
                    "max-age=********; includeSubDomains; preload");
            }

            // Additional security headers
            context.Response.Headers.Add("X-Content-Type-Options", "nosniff");
            context.Response.Headers.Add("X-XSS-Protection", "1; mode=block");

            await next();
        });

        // Development exception page only for development
        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        // Routing setup
        app.UseRouting();
        app.UseConfigRenewal("/Reload");

        // Prometheus monitoring
        app.UseMetricServer();
        app.UseHttpMetrics(opts => opts.AddRouteParameter(new HttpRouteParameterMapping("version", "apiVersion")));

        // Custom middleware
        app.UseCorrelationId();
        //app.UseBrandIdVerifier();
        app.UseAuthentication();
        app.UseAuthorization();

        // Setup endpoints
        app.UseEndpoints(endpoints =>
        {
            endpoints.MapHealthChecks("/health", new HealthCheckOptions
            {
                AllowCachingResponses = true,  // Enable caching where applicable
                Predicate = _ => false,
                ResponseWriter = async (context, _) =>
                    await UIResponseWriter.WriteHealthCheckUIResponse(context, HealthReportCachePublisher.Latest)
            });

            endpoints.MapHealthChecks("/health/active", new HealthCheckOptions
            {
                AllowCachingResponses = true,  // Enable caching where applicable
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
            });

            endpoints.MapControllers();
        });

        // Feature manager async check
        var featureManager = app.ApplicationServices.GetService<IFeatureManager>();
        if (featureManager != null && featureManager.IsEnabledAsync("AllowSwagger").Result)
        {
            app.UseSwagger();
            app.UseVersionedSwaggerUI(provider);
        }

        // Enable AWS S3 Signature Version 4
        AWSConfigsS3.UseSignatureVersion4 = true;
    }

    private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .OrResult(msg => msg.StatusCode == System.Net.HttpStatusCode.NotFound)
            .WaitAndRetryAsync(6, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
    }

    private static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicy()
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .CircuitBreakerAsync(5, TimeSpan.FromSeconds(30));
    }
}