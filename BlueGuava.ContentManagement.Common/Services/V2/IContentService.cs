﻿using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;

//using BlueGuava.Extensions.AWS.QDBL.Models;
using BlueGuava.MarkerManagement.Models;
using Content = BlueGuava.ContentManagement.Packages.Entities.V2.Content;

namespace BlueGuava.ContentManagement.Common.Services.V2;

public interface IContentService
{
    Task<Content?> Retrieve(Guid contentId);
    Task<Content?> Retrieve(string externalId);

    Task<Content?> Create(Content content,
            ClaimsPrincipal user,
            bool isLocationSet = false,
            List<string> organizations = null);

    Task<Content?> Copy(Guid contentId, CopyOverrides overrides, ClaimsPrincipal user, bool changeType);
    Task<Content?> Update(Content content, ClaimsPrincipal user, bool contentAutomation = false);
    Task UpdateForceIndex(Content content);

    /// <summary>
    /// Receives relations from collection service and updates the search index
    /// </summary>
    /// <param name="content"></param>
    /// <returns></returns>
    Task UpdateContentRelations(Content content);

    Task<bool> Remove(Guid contentId, ClaimsPrincipal user);
    Task<bool> SoftRemove(Guid contentId, ClaimsPrincipal user);
    IAsyncEnumerable<Guid> ScanThrough(ContentSearch searchArgs);
    Task<bool> Exist(Content content);
    IAsyncEnumerable<Content?> RetrieveAll();
    Task<string?> GeneratePublicAssetUrl(Guid contentId, Guid assetId);
    Task<Asset?> RetrieveAsset(Guid contentId, Guid assetId);

    /// <summary>
    /// For automation APIs: creates or updates the content
    /// </summary>
    Task<Content?> Save(Content? content, Guid userId, bool maintenance, bool fromJob = false,
        bool fromWriteBack = false);

    //Task<List<EntityHistory<Content>>> ContentHistory(Guid contentId, CancellationToken cancel = default);

    /// <summary>
    /// Triggers an <see cref="InvalidationMark"/> based on ContentId <br/>
    /// this will trigger a migration of all chapter markers for the content into a new marker format
    /// </summary>
    /// <param name="contentId"></param>
    /// <param name="language"></param>
    Task RequestChapterMigration(Guid contentId, string? language);

    Task ContentGenreUpdate(Content entity, Content update = null);

    Task<BlueGuava.Library.Interop.v2.Object> PropertyBag(Guid contentId);
    Task<BlueGuava.Library.Interop.v2.Object> RetrieveFromCacheAndConvert(Guid contentId);

    Task<Content> RetrieveFromCache(Guid contentId);
    Task<int> CopyObject(string sourceBucket, string sourceKey, Guid ownerId, string email, string userData);

    Task UpdateAssetsCDNUrl(Content? content, ClaimsPrincipal user, string oldUrl, string newUrl);

    Task S3CleanupAsync();
    Task S3CopyObjectCleanupAsync();
    Task DeleteS3ObjectsAsync(string bucketName, string folderPrefix);
    Task FixContentDuration(Content entity, Guid userId);
    IAsyncEnumerable<Guid> ScanThroughWithType(int type);

    Task SKUPackageSetup(Content entity, Guid userId, Guid? authGroupId);
    Task CleanDeletedFiles(Content content, Guid userId);

    Task<IEnumerable<ContentPoll>> GetPollsByExternalIdAndReferenceId(string externalId, string referenceId);
    Task<(IEnumerable<object> result, int total)> GetPollsByReferenceId(string referenceId);
    Task SavePoll(ContentPoll poll);
    Task BatchSavePolls(IEnumerable<ContentPoll> polls);
}