﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using BlueGuava.ContentManagement.Common.Entities.V2;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.OrderedSearchResult;

namespace BlueGuava.ContentManagement.Common.Services;

public interface IContentDynamicCatalogService
{
    Task<Library.Interop.v2.Object?> Search(ContentSearch searchArgs, string orderingGroup, string filter, int pageIndex = 0, int? pageSize = null, bool includeOwnerId = false);
    Task<int> Count(ContentSearch searchArgs, string orderingGroup, string filter, int pageIndex = 0, int? pageSize = null);

    Task<(SearchResult<Content>? Hits, IReadOnlyDictionary<string, Dictionary<string, long>> GroupCounts)> Query(ContentSearch searchArgs, string orderingGroup, string filter, int pageIndex = 0, int? pageSize = null, string groupBy = null);
    Task<Library.Interop.v2.Object?> QueryScript(string scriptId, IDictionary<string, object> parameters, int pageSize = 20, int pageIndex = 0, CancellationToken cancel = default);
    Task SyncMustacheScriptsFromFile();

    Task<GroupPageResult> QueryGroupsOnly(
    ContentSearch searchArgs,
    string orderParameter,
    string filterParameter,
    int pageIndex = 0,
    int pageSize = 50,
    string groupBy = null);
}