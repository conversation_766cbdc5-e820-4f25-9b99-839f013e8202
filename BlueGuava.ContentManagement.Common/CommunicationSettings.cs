﻿using System;

namespace BlueGuava.ContentManagement.Common;

public class CommunicationSettings
{
    /// <summary> Number of consecutive failures after the circuit breaker triggers </summary>
    public int CircuitBreakerThreshold { get; set; } = 3;

    /// <summary> Duration until a broken circuit resumes operation </summary>
    public TimeSpan CircuitBreakerDuration { get; set; } = TimeSpan.FromSeconds(10);

    /// <summary> Maximum number of concurrent operations allowed by the bulkhead </summary>
    public int BulkheadMaxConcurrency { get; set; } = 25;

    /// <summary> Maximum number of operations that can be queued when bulkhead is full </summary>
    public int BulkheadMaxQueueSize { get; set; } = 500;
}