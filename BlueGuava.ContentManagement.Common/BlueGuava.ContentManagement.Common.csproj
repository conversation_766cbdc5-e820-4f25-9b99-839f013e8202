﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <LangVersion>12</LangVersion>
        <TargetFrameworks>net8.0</TargetFrameworks>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
        <PackageReference Include="AWSSDK.ChimeSDKMeetings" Version="3.7.400.106" />
        <PackageReference Include="BlueGuava.UserLog.Client" Version="8.1.2" />
        <PackageReference Include="BlueGuava.Collections.Messaging" Version="8.1.2" />
        <PackageReference Include="BlueGuava.Collections.Relationship" Version="8.1.3" />
        <PackageReference Include="BlueGuava.Extensions.AspNetCore.IpAddress" Version="8.1.2" />
        <PackageReference Include="BlueGuava.Extensions.AWS.MaxMindGeoIp" Version="8.1.12" />
        <PackageReference Include="BlueGuava.Extensions.AWS.MessageQueuing.Amazon" Version="8.1.12" />
        <PackageReference Include="BlueGuava.Extensions.AWS.Repositories.ObjectStorage" Version="8.1.12" />
        <PackageReference Include="BlueGuava.Extensions.Configuration" Version="8.1.4" />
        <PackageReference Include="BlueGuava.Extensions.InMemory" Version="8.1.2" />
        <PackageReference Include="BlueGuava.Extensions.Paging.Abstractions" Version="8.1.1" />
        <PackageReference Include="BlueGuava.Extensions.Telemetry" Version="8.1.2" />
        <PackageReference Include="BlueGuava.HealthReporting" Version="8.1.1" />
        <PackageReference Include="BlueGuava.HttpRepository.Abstractions" Version="8.1.1" />
        <PackageReference Include="BlueGuava.ItemsProcessing.Abstractions" Version="8.1.1" />
        <PackageReference Include="BlueGuava.JobManagement.Tracewind" Version="8.1.19" />
        <PackageReference Include="BlueGuava.JwtToken.ClaimFields" Version="8.1.2" />
        <PackageReference Include="BlueGuava.Library.Common" Version="8.1.4" />
        <PackageReference Include="BlueGuava.Library.Delivery.Messaging" Version="8.1.4" />
        <PackageReference Include="BlueGuava.MarkerManagement.Models" Version="8.1.2" />
        <PackageReference Include="BlueGuava.NotificationService.Client" Version="8.1.5" />
        <PackageReference Include="BlueGuava.OrderedSearchResult" Version="8.1.1" />
        <PackageReference Include="BlueGuava.Reporting.Messages" Version="8.0.10" />
        <PackageReference Include="BlueGuava.Tracewind.Common" Version="8.1.1" />
        <PackageReference Include="BlueGuava.Webhook.Messaging" Version="8.1.1" />
        <PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="8.0.0" />
        <PackageReference Include="Microsoft.FeatureManagement" Version="4.0.0" />
        <PackageReference Include="Polly" Version="8.5.2" />
        <PackageReference Include="MethodTimer.Fody" Version="3.2.3" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="BlueGuava.Rewards.Messaging" Version="8.1.5" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\NugetPackages\BlueGuava.ContentManagement.ContentToken\BlueGuava.ContentManagement.ContentToken.csproj" />
        <ProjectReference Include="..\NugetPackages\BlueGuava.ContentManagement.Messages\BlueGuava.ContentManagement.Messages.csproj" />
    </ItemGroup>

</Project>
