using BlueGuava.ContentManagement.Common;
using BlueGuava.ContentManagement.Common.Entities.V2;
using BlueGuava.ContentManagement.Common.Services;
using BlueGuava.ContentManagement.Packages.Entities.V2;
using BlueGuava.Library.Common.BusinessEntities;
using BlueGuava.OrderedSearchResult;

namespace BlueGuava.ContentManagement.IntegrationTests.Fakes;

public class FakeOpenSearch : IOpenSearchService
{
    public Task<SearchResult<Content>?> ExecuteMustache(string scriptId, IDictionary<string, object> parameters, CancellationToken cancel)
    {
        throw new NotImplementedException();
    }

    public Task<SearchResult<Content>?> ExecuteMustache(string scriptId, IDictionary<string, object> parameters, int pageSize, int pageIndex, CancellationToken cancel)
    {
        throw new NotImplementedException();
    }

    /*
public Task<SearchResult<Content>> PublicSearch(PublicContentSearch searchArgs, int pageSize, int pageIndex,
   CancellationToken cancellationToken = default)
{
   throw new NotImplementedException();
}*/

    public Task<SearchResult<Content>?> Search(ContentSearch? searchArgs, int pageSize, int pageIndex, string? sortBy,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<int> Count(ContentSearch? searchArgs, DecisionRules? decisionRules, OrderRules? orderingRules, CancellationToken cancellationToken = default)
    {
        return Task.FromResult(0);
    }

    public Task<(SearchResult<Content>? Hits, IReadOnlyDictionary<string, Dictionary<string, long>> GroupCounts)> Search(ContentSearch? searchArgs, DecisionRules? decisionRules, OrderRules? orderingRules,
        int pageSize, int pageIndex,
        CancellationToken cancellationToken = default,
        IList<string>? groupByFields = null)
    {
        throw new NotImplementedException();
    }

    public Task<GroupPageResult> SearchGroupsOnly(ContentSearch? searchArgs, DecisionRules? decisionRules, OrderRules? orderingRules, int pageSize, int pageIndex, CancellationToken cancellationToken = default, IList<string>? groupByFields = null)
    {
        throw new NotImplementedException();
    }

    public Task<SyncResult> SyncMustacheScriptsFromFile(string jsonFilePath, CancellationToken cancel)
    {
        throw new NotImplementedException();
    }

    public Task<SyncResult> SyncMustacheScriptsFromFile()
    {
        throw new NotImplementedException();
    }
}

public class FakeSearchAdapter : ISearchAdapter
{
    public Task Delete(Guid id, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    public Task InitializeIndex(CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    public Task Save(Content entity, CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    public Task DeleteIndex(CancellationToken cancellationToken = default)
    {
        return Task.CompletedTask;
    }

    public Task<bool> IsHealthy()
    {
        throw new NotImplementedException();
    }
}